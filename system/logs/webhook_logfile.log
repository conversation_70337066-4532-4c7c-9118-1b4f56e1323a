[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 22:36:08
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:36]  Provided signature: sha256=4f1664e59ba73afc609f8e2335f93792be9f06b31dfc1f4cf560b4ef3ad20884
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:37]  Calculated signature: sha256=4f1664e59ba73afc609f8e2335f93792be9f06b31dfc1f4cf560b4ef3ad20884
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bc7df98936975259\n    [X-B3-Traceid] => 68b8c2d5511863df36a05d203f1dfcd9\n    [B3] => 68b8c2d5511863df36a05d203f1dfcd9-bc7df98936975259-1\n    [Traceparent] => 00-68b8c2d5511863df36a05d203f1dfcd9-bc7df98936975259-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c2d5-511863df36a05d203f1dfcd9;Parent=bc7df98936975259;Sampled=1\n    [X-Adsk-Signature] => sha256=4f1664e59ba73afc609f8e2335f93792be9f06b31dfc1f4cf560b4ef3ad20884\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756938965774-56777717908679-9033825183-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756938965774-56777717908679-9033825183-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56777717908679","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-03T22:36:05.774Z"},"publishedAt":"2025-09-03T22:36:05.000Z","csn":"5103159758"}
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 22:36:08
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:36]  Provided signature: sha256=b719fcfefd4cf0ed69df7791e6a9322cf0d8a9d2bdf923f64a1ad8c2356cd4d7
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:37]  Calculated signature: sha256=4f1664e59ba73afc609f8e2335f93792be9f06b31dfc1f4cf560b4ef3ad20884
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 91e981a225d4a4be\n    [X-B3-Traceid] => 68b8c2d5511863df36a05d203f1dfcd9\n    [B3] => 68b8c2d5511863df36a05d203f1dfcd9-91e981a225d4a4be-1\n    [Traceparent] => 00-68b8c2d5511863df36a05d203f1dfcd9-91e981a225d4a4be-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c2d5-511863df36a05d203f1dfcd9;Parent=91e981a225d4a4be;Sampled=1\n    [X-Adsk-Signature] => sha256=b719fcfefd4cf0ed69df7791e6a9322cf0d8a9d2bdf923f64a1ad8c2356cd4d7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756938965774-56777717908679-9033825183-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 22:36:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756938965774-56777717908679-9033825183-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56777717908679","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-03T22:36:05.774Z"},"publishedAt":"2025-09-03T22:36:05.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:34] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:34
[webhook] [2025-09-03 23:00:34] [adwsapi_v2.php:36]  Provided signature: sha256=0332c696d1a6d9e3b35d6d779e9d67c8b5eaa8d164572b18fc803e4fffcfbf58
[webhook] [2025-09-03 23:00:34] [adwsapi_v2.php:37]  Calculated signature: sha256=f04c550614a03c46c1adcdf62a86963ff31457e3792282db403f3b28af9fa294
[webhook] [2025-09-03 23:00:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2374f6c2ea365e19\n    [X-B3-Traceid] => 68b8c88ce08c432f6c87e810a99eebb0\n    [B3] => 68b8c88ce08c432f6c87e810a99eebb0-2374f6c2ea365e19-1\n    [Traceparent] => 00-68b8c88ce08c432f6c87e810a99eebb0-2374f6c2ea365e19-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c88c-e08c432f6c87e810a99eebb0;Parent=2374f6c2ea365e19;Sampled=1\n    [X-Adsk-Signature] => sha256=0332c696d1a6d9e3b35d6d779e9d67c8b5eaa8d164572b18fc803e4fffcfbf58\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6f60671c-349e-4743-a214-286b398e4676\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"6f60671c-349e-4743-a214-286b398e4676","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979451","transactionId":"86140b3f-8aa6-542a-ae56-6a35e0d76b4b","quoteStatus":"Expired","message":"Quote# Q-979451 status changed to Expired.","modifiedAt":"2025-09-03T23:00:27.942Z"},"publishedAt":"2025-09-03T23:00:32.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:35] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:35
[webhook] [2025-09-03 23:00:35] [adwsapi_v2.php:36]  Provided signature: sha256=f04c550614a03c46c1adcdf62a86963ff31457e3792282db403f3b28af9fa294
[webhook] [2025-09-03 23:00:35] [adwsapi_v2.php:37]  Calculated signature: sha256=f04c550614a03c46c1adcdf62a86963ff31457e3792282db403f3b28af9fa294
[webhook] [2025-09-03 23:00:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => df65cefaaad2e2a5\n    [X-B3-Traceid] => 68b8c88ce08c432f6c87e810a99eebb0\n    [B3] => 68b8c88ce08c432f6c87e810a99eebb0-df65cefaaad2e2a5-1\n    [Traceparent] => 00-68b8c88ce08c432f6c87e810a99eebb0-df65cefaaad2e2a5-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c88c-e08c432f6c87e810a99eebb0;Parent=df65cefaaad2e2a5;Sampled=1\n    [X-Adsk-Signature] => sha256=f04c550614a03c46c1adcdf62a86963ff31457e3792282db403f3b28af9fa294\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6f60671c-349e-4743-a214-286b398e4676\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"6f60671c-349e-4743-a214-286b398e4676","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979451","transactionId":"86140b3f-8aa6-542a-ae56-6a35e0d76b4b","quoteStatus":"Expired","message":"Quote# Q-979451 status changed to Expired.","modifiedAt":"2025-09-03T23:00:27.942Z"},"publishedAt":"2025-09-03T23:00:32.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:37
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:36]  Provided signature: sha256=76f8f2c2d5e8897e543b342b911a30fdc4256a2a449fb9a477ebb7b7eac88d6f
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:37]  Calculated signature: sha256=4a8f82f6a4b0fab1eb5e53d1d3d1811385f06664fa2c0e1233580c3421dca3fc
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => da452f0d16282fbc\n    [X-B3-Traceid] => 68b8c8906b65c04b06c7e66406327801\n    [B3] => 68b8c8906b65c04b06c7e66406327801-da452f0d16282fbc-1\n    [Traceparent] => 00-68b8c8906b65c04b06c7e66406327801-da452f0d16282fbc-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c890-6b65c04b06c7e66406327801;Parent=da452f0d16282fbc;Sampled=1\n    [X-Adsk-Signature] => sha256=76f8f2c2d5e8897e543b342b911a30fdc4256a2a449fb9a477ebb7b7eac88d6f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4eb86490-f9bd-4b17-830c-3ee425b5b84a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"4eb86490-f9bd-4b17-830c-3ee425b5b84a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979508","transactionId":"53be095e-f7bc-5167-8f04-68549b3bbc39","quoteStatus":"Expired","message":"Quote# Q-979508 status changed to Expired.","modifiedAt":"2025-09-03T23:00:31.875Z"},"publishedAt":"2025-09-03T23:00:34.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:37
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:36]  Provided signature: sha256=4a8f82f6a4b0fab1eb5e53d1d3d1811385f06664fa2c0e1233580c3421dca3fc
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:37]  Calculated signature: sha256=4a8f82f6a4b0fab1eb5e53d1d3d1811385f06664fa2c0e1233580c3421dca3fc
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => df2114b29dc903ea\n    [X-B3-Traceid] => 68b8c8906b65c04b06c7e66406327801\n    [B3] => 68b8c8906b65c04b06c7e66406327801-df2114b29dc903ea-1\n    [Traceparent] => 00-68b8c8906b65c04b06c7e66406327801-df2114b29dc903ea-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c890-6b65c04b06c7e66406327801;Parent=df2114b29dc903ea;Sampled=1\n    [X-Adsk-Signature] => sha256=4a8f82f6a4b0fab1eb5e53d1d3d1811385f06664fa2c0e1233580c3421dca3fc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4eb86490-f9bd-4b17-830c-3ee425b5b84a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"4eb86490-f9bd-4b17-830c-3ee425b5b84a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979508","transactionId":"53be095e-f7bc-5167-8f04-68549b3bbc39","quoteStatus":"Expired","message":"Quote# Q-979508 status changed to Expired.","modifiedAt":"2025-09-03T23:00:31.875Z"},"publishedAt":"2025-09-03T23:00:34.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:42
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:36]  Provided signature: sha256=47a0303ba6573f3fae1eb08f12574290a9879c4b54350ea48d0abf2f5ad9520e
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:37]  Calculated signature: sha256=9fac0caeb60a05bffadf0b3e725acbc536d238a4cce56ebbb3fbd28525f0b05d
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a3369241f77ba596\n    [X-B3-Traceid] => 68b8c8972be23b36e0ac3e7229543e1d\n    [B3] => 68b8c8972be23b36e0ac3e7229543e1d-a3369241f77ba596-1\n    [Traceparent] => 00-68b8c8972be23b36e0ac3e7229543e1d-a3369241f77ba596-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c897-2be23b36e0ac3e7229543e1d;Parent=a3369241f77ba596;Sampled=1\n    [X-Adsk-Signature] => sha256=47a0303ba6573f3fae1eb08f12574290a9879c4b54350ea48d0abf2f5ad9520e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c0d1e570-174c-405c-8972-1056eef53032\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"c0d1e570-174c-405c-8972-1056eef53032","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979616","transactionId":"576afb74-fd9f-556a-ac25-9a7e8d16f607","quoteStatus":"Expired","message":"Quote# Q-979616 status changed to Expired.","modifiedAt":"2025-09-03T23:00:39.443Z"},"publishedAt":"2025-09-03T23:00:40.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:42
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:36]  Provided signature: sha256=9fac0caeb60a05bffadf0b3e725acbc536d238a4cce56ebbb3fbd28525f0b05d
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:37]  Calculated signature: sha256=9fac0caeb60a05bffadf0b3e725acbc536d238a4cce56ebbb3fbd28525f0b05d
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2d52e5ff8e17dde8\n    [X-B3-Traceid] => 68b8c8972be23b36e0ac3e7229543e1d\n    [B3] => 68b8c8972be23b36e0ac3e7229543e1d-2d52e5ff8e17dde8-1\n    [Traceparent] => 00-68b8c8972be23b36e0ac3e7229543e1d-2d52e5ff8e17dde8-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c897-2be23b36e0ac3e7229543e1d;Parent=2d52e5ff8e17dde8;Sampled=1\n    [X-Adsk-Signature] => sha256=9fac0caeb60a05bffadf0b3e725acbc536d238a4cce56ebbb3fbd28525f0b05d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c0d1e570-174c-405c-8972-1056eef53032\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"c0d1e570-174c-405c-8972-1056eef53032","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979616","transactionId":"576afb74-fd9f-556a-ac25-9a7e8d16f607","quoteStatus":"Expired","message":"Quote# Q-979616 status changed to Expired.","modifiedAt":"2025-09-03T23:00:39.443Z"},"publishedAt":"2025-09-03T23:00:40.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:44
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:36]  Provided signature: sha256=7010cfe119fcc2912ec151b2961e83755b6ae85c5d430a8c2a7c3278b81e2547
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:37]  Calculated signature: sha256=7010cfe119fcc2912ec151b2961e83755b6ae85c5d430a8c2a7c3278b81e2547
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b80182cb48a8a019\n    [X-B3-Traceid] => 68b8c89a3be7532cb4f36930a004e85a\n    [B3] => 68b8c89a3be7532cb4f36930a004e85a-b80182cb48a8a019-1\n    [Traceparent] => 00-68b8c89a3be7532cb4f36930a004e85a-b80182cb48a8a019-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c89a-3be7532cb4f36930a004e85a;Parent=b80182cb48a8a019;Sampled=1\n    [X-Adsk-Signature] => sha256=7010cfe119fcc2912ec151b2961e83755b6ae85c5d430a8c2a7c3278b81e2547\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4a02768d-08bc-4c9f-8aa5-790465bb2c99\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"4a02768d-08bc-4c9f-8aa5-790465bb2c99","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979541","transactionId":"6d41fdac-6f14-5bb5-b4fa-9ca60280c767","quoteStatus":"Expired","message":"Quote# Q-979541 status changed to Expired.","modifiedAt":"2025-09-03T23:00:39.134Z"},"publishedAt":"2025-09-03T23:00:42.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:44
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:36]  Provided signature: sha256=123cf27433595fb712ce580a4a4f9f889e636b72b9ced9ec0e8d0c173fb4cdc5
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:37]  Calculated signature: sha256=7010cfe119fcc2912ec151b2961e83755b6ae85c5d430a8c2a7c3278b81e2547
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c26bc90b13b9eb41\n    [X-B3-Traceid] => 68b8c89a3be7532cb4f36930a004e85a\n    [B3] => 68b8c89a3be7532cb4f36930a004e85a-c26bc90b13b9eb41-1\n    [Traceparent] => 00-68b8c89a3be7532cb4f36930a004e85a-c26bc90b13b9eb41-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c89a-3be7532cb4f36930a004e85a;Parent=c26bc90b13b9eb41;Sampled=1\n    [X-Adsk-Signature] => sha256=123cf27433595fb712ce580a4a4f9f889e636b72b9ced9ec0e8d0c173fb4cdc5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4a02768d-08bc-4c9f-8aa5-790465bb2c99\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:44] [adwsapi_v2.php:57]  Received webhook data: {"id":"4a02768d-08bc-4c9f-8aa5-790465bb2c99","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979541","transactionId":"6d41fdac-6f14-5bb5-b4fa-9ca60280c767","quoteStatus":"Expired","message":"Quote# Q-979541 status changed to Expired.","modifiedAt":"2025-09-03T23:00:39.134Z"},"publishedAt":"2025-09-03T23:00:42.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:46
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:36]  Provided signature: sha256=973d4414bcc86e7405b441ebeeb4df6ef7257177e92f17a4827bf8a8e740421c
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:37]  Calculated signature: sha256=a7887656e4825eb1e22df9a8f9037595093eed9aa5022bbba42fdf3616c13517
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4e94313d4662184f\n    [X-B3-Traceid] => 68b8c899d4e3f0a7e0253d364317b96b\n    [B3] => 68b8c899d4e3f0a7e0253d364317b96b-4e94313d4662184f-1\n    [Traceparent] => 00-68b8c899d4e3f0a7e0253d364317b96b-4e94313d4662184f-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c899-d4e3f0a7e0253d364317b96b;Parent=4e94313d4662184f;Sampled=1\n    [X-Adsk-Signature] => sha256=973d4414bcc86e7405b441ebeeb4df6ef7257177e92f17a4827bf8a8e740421c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 26d1372d-8149-4d0e-be89-da558b6e2591\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"26d1372d-8149-4d0e-be89-da558b6e2591","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979601","transactionId":"87e453b3-75e8-5ae5-9b2a-32b905e7205d","quoteStatus":"Expired","message":"Quote# Q-979601 status changed to Expired.","modifiedAt":"2025-09-03T23:00:39.454Z"},"publishedAt":"2025-09-03T23:00:41.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:46
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:36]  Provided signature: sha256=a43f7894f92454a8aba0da1cc36cfaa5b5526b66fb2d4aba5792efc42a8cf966
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:37]  Calculated signature: sha256=38fe089c5e56a4ae83d7b44757d9c71e867e756d0bfd12c07fe66cf332f0f498
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 57880561007819a2\n    [X-B3-Traceid] => 68b8c89b9df564abd1d0e880a229c2ca\n    [B3] => 68b8c89b9df564abd1d0e880a229c2ca-57880561007819a2-1\n    [Traceparent] => 00-68b8c89b9df564abd1d0e880a229c2ca-57880561007819a2-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c89b-9df564abd1d0e880a229c2ca;Parent=57880561007819a2;Sampled=1\n    [X-Adsk-Signature] => sha256=a43f7894f92454a8aba0da1cc36cfaa5b5526b66fb2d4aba5792efc42a8cf966\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => fdf10e79-c870-42e1-a902-70516746da1d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"fdf10e79-c870-42e1-a902-70516746da1d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-980363","transactionId":"afbabe9a-6dc7-5fc5-8d17-df90f5a88454","quoteStatus":"Expired","message":"Quote# Q-980363 status changed to Expired.","modifiedAt":"2025-09-03T23:00:43.621Z"},"publishedAt":"2025-09-03T23:00:44.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:46
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:36]  Provided signature: sha256=38fe089c5e56a4ae83d7b44757d9c71e867e756d0bfd12c07fe66cf332f0f498
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:37]  Calculated signature: sha256=38fe089c5e56a4ae83d7b44757d9c71e867e756d0bfd12c07fe66cf332f0f498
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f62ebda98e63da31\n    [X-B3-Traceid] => 68b8c89b9df564abd1d0e880a229c2ca\n    [B3] => 68b8c89b9df564abd1d0e880a229c2ca-f62ebda98e63da31-1\n    [Traceparent] => 00-68b8c89b9df564abd1d0e880a229c2ca-f62ebda98e63da31-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c89b-9df564abd1d0e880a229c2ca;Parent=f62ebda98e63da31;Sampled=1\n    [X-Adsk-Signature] => sha256=38fe089c5e56a4ae83d7b44757d9c71e867e756d0bfd12c07fe66cf332f0f498\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => fdf10e79-c870-42e1-a902-70516746da1d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"fdf10e79-c870-42e1-a902-70516746da1d","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-980363","transactionId":"afbabe9a-6dc7-5fc5-8d17-df90f5a88454","quoteStatus":"Expired","message":"Quote# Q-980363 status changed to Expired.","modifiedAt":"2025-09-03T23:00:43.621Z"},"publishedAt":"2025-09-03T23:00:44.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:46
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:36]  Provided signature: sha256=00f1a83f2016d1ef0a0434c80c34e3860740e3644c308abb98e94370dd59876a
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:37]  Calculated signature: sha256=00f1a83f2016d1ef0a0434c80c34e3860740e3644c308abb98e94370dd59876a
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 72283b631edf6988\n    [X-B3-Traceid] => 68b8c89c5e446c788bdf940ff222ef11\n    [B3] => 68b8c89c5e446c788bdf940ff222ef11-72283b631edf6988-1\n    [Traceparent] => 00-68b8c89c5e446c788bdf940ff222ef11-72283b631edf6988-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c89c-5e446c788bdf940ff222ef11;Parent=72283b631edf6988;Sampled=1\n    [X-Adsk-Signature] => sha256=00f1a83f2016d1ef0a0434c80c34e3860740e3644c308abb98e94370dd59876a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 69ab96d5-4cf9-472a-88cc-1324a087ed28\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"69ab96d5-4cf9-472a-88cc-1324a087ed28","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979487","transactionId":"0c4a4009-2fe6-507e-a177-210344555a42","quoteStatus":"Expired","message":"Quote# Q-979487 status changed to Expired.","modifiedAt":"2025-09-03T23:00:43.701Z"},"publishedAt":"2025-09-03T23:00:44.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:46
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:36]  Provided signature: sha256=a7887656e4825eb1e22df9a8f9037595093eed9aa5022bbba42fdf3616c13517
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:37]  Calculated signature: sha256=a7887656e4825eb1e22df9a8f9037595093eed9aa5022bbba42fdf3616c13517
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a2c6c26de4f7e152\n    [X-B3-Traceid] => 68b8c899d4e3f0a7e0253d364317b96b\n    [B3] => 68b8c899d4e3f0a7e0253d364317b96b-a2c6c26de4f7e152-1\n    [Traceparent] => 00-68b8c899d4e3f0a7e0253d364317b96b-a2c6c26de4f7e152-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c899-d4e3f0a7e0253d364317b96b;Parent=a2c6c26de4f7e152;Sampled=1\n    [X-Adsk-Signature] => sha256=a7887656e4825eb1e22df9a8f9037595093eed9aa5022bbba42fdf3616c13517\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 26d1372d-8149-4d0e-be89-da558b6e2591\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"26d1372d-8149-4d0e-be89-da558b6e2591","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979601","transactionId":"87e453b3-75e8-5ae5-9b2a-32b905e7205d","quoteStatus":"Expired","message":"Quote# Q-979601 status changed to Expired.","modifiedAt":"2025-09-03T23:00:39.454Z"},"publishedAt":"2025-09-03T23:00:41.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:46
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:36]  Provided signature: sha256=fc919095530e515755f80403fb1a3dae1c13fbb2c091021191444f3d723e3e0e
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:37]  Calculated signature: sha256=00f1a83f2016d1ef0a0434c80c34e3860740e3644c308abb98e94370dd59876a
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c9b30325b27536d5\n    [X-B3-Traceid] => 68b8c89c5e446c788bdf940ff222ef11\n    [B3] => 68b8c89c5e446c788bdf940ff222ef11-c9b30325b27536d5-1\n    [Traceparent] => 00-68b8c89c5e446c788bdf940ff222ef11-c9b30325b27536d5-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c89c-5e446c788bdf940ff222ef11;Parent=c9b30325b27536d5;Sampled=1\n    [X-Adsk-Signature] => sha256=fc919095530e515755f80403fb1a3dae1c13fbb2c091021191444f3d723e3e0e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 69ab96d5-4cf9-472a-88cc-1324a087ed28\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"69ab96d5-4cf9-472a-88cc-1324a087ed28","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979487","transactionId":"0c4a4009-2fe6-507e-a177-210344555a42","quoteStatus":"Expired","message":"Quote# Q-979487 status changed to Expired.","modifiedAt":"2025-09-03T23:00:43.701Z"},"publishedAt":"2025-09-03T23:00:44.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:47] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:47
[webhook] [2025-09-03 23:00:47] [adwsapi_v2.php:36]  Provided signature: sha256=f35063786e7c72907231940ec463c0ea7a3b39830c65a1c26a8c4cd9870242ba
[webhook] [2025-09-03 23:00:47] [adwsapi_v2.php:37]  Calculated signature: sha256=f35063786e7c72907231940ec463c0ea7a3b39830c65a1c26a8c4cd9870242ba
[webhook] [2025-09-03 23:00:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2849b6506b786b22\n    [X-B3-Traceid] => 68b8c89d37f5afd418500439e800666b\n    [B3] => 68b8c89d37f5afd418500439e800666b-2849b6506b786b22-1\n    [Traceparent] => 00-68b8c89d37f5afd418500439e800666b-2849b6506b786b22-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c89d-37f5afd418500439e800666b;Parent=2849b6506b786b22;Sampled=1\n    [X-Adsk-Signature] => sha256=f35063786e7c72907231940ec463c0ea7a3b39830c65a1c26a8c4cd9870242ba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 248e670b-20bd-407c-9963-1ded957e1f1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"248e670b-20bd-407c-9963-1ded957e1f1b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979608","transactionId":"19220073-ed75-555b-9159-61799d4006dc","quoteStatus":"Expired","message":"Quote# Q-979608 status changed to Expired.","modifiedAt":"2025-09-03T23:00:44.626Z"},"publishedAt":"2025-09-03T23:00:45.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:00:48] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:00:48
[webhook] [2025-09-03 23:00:48] [adwsapi_v2.php:36]  Provided signature: sha256=6dce0a4f46396255a3b582b99ea13f1ca3e3456ca79c3ec7b71064b0ac79646a
[webhook] [2025-09-03 23:00:48] [adwsapi_v2.php:37]  Calculated signature: sha256=f35063786e7c72907231940ec463c0ea7a3b39830c65a1c26a8c4cd9870242ba
[webhook] [2025-09-03 23:00:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c269518912856d51\n    [X-B3-Traceid] => 68b8c89d37f5afd418500439e800666b\n    [B3] => 68b8c89d37f5afd418500439e800666b-c269518912856d51-1\n    [Traceparent] => 00-68b8c89d37f5afd418500439e800666b-c269518912856d51-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c89d-37f5afd418500439e800666b;Parent=c269518912856d51;Sampled=1\n    [X-Adsk-Signature] => sha256=6dce0a4f46396255a3b582b99ea13f1ca3e3456ca79c3ec7b71064b0ac79646a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 248e670b-20bd-407c-9963-1ded957e1f1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:00:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"248e670b-20bd-407c-9963-1ded957e1f1b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-979608","transactionId":"19220073-ed75-555b-9159-61799d4006dc","quoteStatus":"Expired","message":"Quote# Q-979608 status changed to Expired.","modifiedAt":"2025-09-03T23:00:44.626Z"},"publishedAt":"2025-09-03T23:00:45.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:01:21
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:36]  Provided signature: sha256=fd93a2b7b8d74200b4f3f371d4f2c235d77d05ccb8d5f25b9a51a764b7387b02
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:37]  Calculated signature: sha256=fd4550a6bde9914fceac11a0592ad0b58de6272751e417caf616fc7c2737d380
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 801b33bce441108e\n    [X-B3-Traceid] => 68b8c8bf67290ede0d25b9574d6d2ffb\n    [B3] => 68b8c8bf67290ede0d25b9574d6d2ffb-801b33bce441108e-1\n    [Traceparent] => 00-68b8c8bf67290ede0d25b9574d6d2ffb-801b33bce441108e-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c8bf-67290ede0d25b9574d6d2ffb;Parent=801b33bce441108e;Sampled=1\n    [X-Adsk-Signature] => sha256=fd93a2b7b8d74200b4f3f371d4f2c235d77d05ccb8d5f25b9a51a764b7387b02\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756940479329-559-12144836\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756940479329-559-12144836","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"559-12144836","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-09-03T23:01:19.329Z"},"publishedAt":"2025-09-03T23:01:19.000Z","csn":"5103159758"}
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:20]  Webhook request received at 2025-09-03 23:01:21
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:36]  Provided signature: sha256=fd4550a6bde9914fceac11a0592ad0b58de6272751e417caf616fc7c2737d380
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:37]  Calculated signature: sha256=fd4550a6bde9914fceac11a0592ad0b58de6272751e417caf616fc7c2737d380
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1fc5595898bc3166\n    [X-B3-Traceid] => 68b8c8bf67290ede0d25b9574d6d2ffb\n    [B3] => 68b8c8bf67290ede0d25b9574d6d2ffb-1fc5595898bc3166-1\n    [Traceparent] => 00-68b8c8bf67290ede0d25b9574d6d2ffb-1fc5595898bc3166-01\n    [X-Amzn-Trace-Id] => Root=1-68b8c8bf-67290ede0d25b9574d6d2ffb;Parent=1fc5595898bc3166;Sampled=1\n    [X-Adsk-Signature] => sha256=fd4550a6bde9914fceac11a0592ad0b58de6272751e417caf616fc7c2737d380\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756940479329-559-12144836\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-03 23:01:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756940479329-559-12144836","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"559-12144836","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-09-03T23:01:19.329Z"},"publishedAt":"2025-09-03T23:01:19.000Z","csn":"5103159758"}
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 00:01:01
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:36]  Provided signature: sha256=7d8d8d39db8d1b846f0d38ededf8943191b9017ff699ef8bd8e244cb871c8b87
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:37]  Calculated signature: sha256=ff46ddf4872f53663388261938fd9f501a84e4b06033abb56888e0bb2a0da337
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 16edede0966f829e\n    [X-B3-Traceid] => 68b8d6ba6de410641140918a0057bf82\n    [B3] => 68b8d6ba6de410641140918a0057bf82-16edede0966f829e-1\n    [Traceparent] => 00-68b8d6ba6de410641140918a0057bf82-16edede0966f829e-01\n    [X-Amzn-Trace-Id] => Root=1-68b8d6ba-6de410641140918a0057bf82;Parent=16edede0966f829e;Sampled=1\n    [X-Adsk-Signature] => sha256=7d8d8d39db8d1b846f0d38ededf8943191b9017ff699ef8bd8e244cb871c8b87\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8292ea3f-ccde-41b2-a8b0-74e9f7e343dc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"8292ea3f-ccde-41b2-a8b0-74e9f7e343dc","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-09-04T00:00:58Z"},"publishedAt":"2025-09-04T00:00:58.000Z","country":"GB"}
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 00:01:01
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:36]  Provided signature: sha256=ff46ddf4872f53663388261938fd9f501a84e4b06033abb56888e0bb2a0da337
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:37]  Calculated signature: sha256=ff46ddf4872f53663388261938fd9f501a84e4b06033abb56888e0bb2a0da337
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 93e76ca4c89be37f\n    [X-B3-Traceid] => 68b8d6ba6de410641140918a0057bf82\n    [B3] => 68b8d6ba6de410641140918a0057bf82-93e76ca4c89be37f-1\n    [Traceparent] => 00-68b8d6ba6de410641140918a0057bf82-93e76ca4c89be37f-01\n    [X-Amzn-Trace-Id] => Root=1-68b8d6ba-6de410641140918a0057bf82;Parent=93e76ca4c89be37f;Sampled=1\n    [X-Adsk-Signature] => sha256=ff46ddf4872f53663388261938fd9f501a84e4b06033abb56888e0bb2a0da337\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8292ea3f-ccde-41b2-a8b0-74e9f7e343dc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 00:01:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"8292ea3f-ccde-41b2-a8b0-74e9f7e343dc","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-09-04T00:00:58Z"},"publishedAt":"2025-09-04T00:00:58.000Z","country":"GB"}
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 00:01:43
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:36]  Provided signature: sha256=46b2e55c002d170ff469a123d8dd7f5e655377daf137fb840da9a4feea719f0d
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:37]  Calculated signature: sha256=5876b32e9f6730985c3796485d65fa97750ac9a8b705d812025bec7b3b300c05
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e652a0d5b0ec5364\n    [X-B3-Traceid] => 68b8d6e57842f60d5c2ecc830a326ad3\n    [B3] => 68b8d6e57842f60d5c2ecc830a326ad3-e652a0d5b0ec5364-1\n    [Traceparent] => 00-68b8d6e57842f60d5c2ecc830a326ad3-e652a0d5b0ec5364-01\n    [X-Amzn-Trace-Id] => Root=1-68b8d6e5-7842f60d5c2ecc830a326ad3;Parent=e652a0d5b0ec5364;Sampled=1\n    [X-Adsk-Signature] => sha256=46b2e55c002d170ff469a123d8dd7f5e655377daf137fb840da9a4feea719f0d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3023f2ca-fd8f-417f-9a2e-eec76797e29d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"3023f2ca-fd8f-417f-9a2e-eec76797e29d","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Price_Change","modifiedAt":"2025-09-04T00:01:41Z"},"publishedAt":"2025-09-04T00:01:41.000Z","country":"GB"}
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 00:01:43
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:36]  Provided signature: sha256=5876b32e9f6730985c3796485d65fa97750ac9a8b705d812025bec7b3b300c05
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:37]  Calculated signature: sha256=5876b32e9f6730985c3796485d65fa97750ac9a8b705d812025bec7b3b300c05
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1b0f7e7443bab1d3\n    [X-B3-Traceid] => 68b8d6e57842f60d5c2ecc830a326ad3\n    [B3] => 68b8d6e57842f60d5c2ecc830a326ad3-1b0f7e7443bab1d3-1\n    [Traceparent] => 00-68b8d6e57842f60d5c2ecc830a326ad3-1b0f7e7443bab1d3-01\n    [X-Amzn-Trace-Id] => Root=1-68b8d6e5-7842f60d5c2ecc830a326ad3;Parent=1b0f7e7443bab1d3;Sampled=1\n    [X-Adsk-Signature] => sha256=5876b32e9f6730985c3796485d65fa97750ac9a8b705d812025bec7b3b300c05\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3023f2ca-fd8f-417f-9a2e-eec76797e29d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 00:01:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"3023f2ca-fd8f-417f-9a2e-eec76797e29d","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Price_Change","modifiedAt":"2025-09-04T00:01:41Z"},"publishedAt":"2025-09-04T00:01:41.000Z","country":"GB"}
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 07:07:05
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:36]  Provided signature: sha256=ab8a31a665821e4168d403b556d13626b0117f9f0d4f57981a7c6b997eb7aee6
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:37]  Calculated signature: sha256=ab8a31a665821e4168d403b556d13626b0117f9f0d4f57981a7c6b997eb7aee6
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 45a0a3b29d1d0d83\n    [X-B3-Traceid] => 68b93a976218ef426fd5e6d60e5ed581\n    [B3] => 68b93a976218ef426fd5e6d60e5ed581-45a0a3b29d1d0d83-1\n    [Traceparent] => 00-68b93a976218ef426fd5e6d60e5ed581-45a0a3b29d1d0d83-01\n    [X-Amzn-Trace-Id] => Root=1-68b93a97-6218ef426fd5e6d60e5ed581;Parent=45a0a3b29d1d0d83;Sampled=1\n    [X-Adsk-Signature] => sha256=ab8a31a665821e4168d403b556d13626b0117f9f0d4f57981a7c6b997eb7aee6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => df08f4d9-7dbf-4301-bc8c-432e2c28e8b6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"df08f4d9-7dbf-4301-bc8c-432e2c28e8b6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66187025913229","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T06:46:59.000+0000"},"publishedAt":"2025-09-04T07:07:03.000Z","csn":"5103159758"}
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 07:07:05
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:36]  Provided signature: sha256=8f538b10b0f68ace220ae770cc093323db16020f156a38ba446dd164547658d6
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:37]  Calculated signature: sha256=ab8a31a665821e4168d403b556d13626b0117f9f0d4f57981a7c6b997eb7aee6
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d5519bfed3fe07ed\n    [X-B3-Traceid] => 68b93a976218ef426fd5e6d60e5ed581\n    [B3] => 68b93a976218ef426fd5e6d60e5ed581-d5519bfed3fe07ed-1\n    [Traceparent] => 00-68b93a976218ef426fd5e6d60e5ed581-d5519bfed3fe07ed-01\n    [X-Amzn-Trace-Id] => Root=1-68b93a97-6218ef426fd5e6d60e5ed581;Parent=d5519bfed3fe07ed;Sampled=1\n    [X-Adsk-Signature] => sha256=8f538b10b0f68ace220ae770cc093323db16020f156a38ba446dd164547658d6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => df08f4d9-7dbf-4301-bc8c-432e2c28e8b6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 07:07:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"df08f4d9-7dbf-4301-bc8c-432e2c28e8b6","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66187025913229","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T06:46:59.000+0000"},"publishedAt":"2025-09-04T07:07:03.000Z","csn":"5103159758"}
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 07:11:08
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:36]  Provided signature: sha256=cae03adc8de87f37bbc00a6c32facdca1b9bd9df8934c70dad54dfbac688f782
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:37]  Calculated signature: sha256=cae03adc8de87f37bbc00a6c32facdca1b9bd9df8934c70dad54dfbac688f782
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 20dcd76fea215ab7\n    [X-B3-Traceid] => 68b93b8a6ea59ef30f6e5c057305ed74\n    [B3] => 68b93b8a6ea59ef30f6e5c057305ed74-20dcd76fea215ab7-1\n    [Traceparent] => 00-68b93b8a6ea59ef30f6e5c057305ed74-20dcd76fea215ab7-01\n    [X-Amzn-Trace-Id] => Root=1-68b93b8a-6ea59ef30f6e5c057305ed74;Parent=20dcd76fea215ab7;Sampled=1\n    [X-Adsk-Signature] => sha256=cae03adc8de87f37bbc00a6c32facdca1b9bd9df8934c70dad54dfbac688f782\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 66d24bf1-bb3c-4a7b-96e7-4d198ee11b65\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"66d24bf1-bb3c-4a7b-96e7-4d198ee11b65","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66264904812166","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T06:43:38.000+0000"},"publishedAt":"2025-09-04T07:11:06.000Z","csn":"5103159758"}
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 07:11:08
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:36]  Provided signature: sha256=0c39466e897164f8dc3c639536bb46348cac4c2cc35a38e95021a6565bec541c
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:37]  Calculated signature: sha256=cae03adc8de87f37bbc00a6c32facdca1b9bd9df8934c70dad54dfbac688f782
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ffe81d7718a50f58\n    [X-B3-Traceid] => 68b93b8a6ea59ef30f6e5c057305ed74\n    [B3] => 68b93b8a6ea59ef30f6e5c057305ed74-ffe81d7718a50f58-1\n    [Traceparent] => 00-68b93b8a6ea59ef30f6e5c057305ed74-ffe81d7718a50f58-01\n    [X-Amzn-Trace-Id] => Root=1-68b93b8a-6ea59ef30f6e5c057305ed74;Parent=ffe81d7718a50f58;Sampled=1\n    [X-Adsk-Signature] => sha256=0c39466e897164f8dc3c639536bb46348cac4c2cc35a38e95021a6565bec541c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 66d24bf1-bb3c-4a7b-96e7-4d198ee11b65\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 07:11:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"66d24bf1-bb3c-4a7b-96e7-4d198ee11b65","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66264904812166","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T06:43:38.000+0000"},"publishedAt":"2025-09-04T07:11:06.000Z","csn":"5103159758"}
[webhook] [2025-09-04 07:58:55] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 07:58:55
[webhook] [2025-09-04 07:58:55] [adwsapi_v2.php:36]  Provided signature: sha256=74f782a7d558636645c043d40ebed055a7ceea6f9a07d326563dba9853287bfc
[webhook] [2025-09-04 07:58:55] [adwsapi_v2.php:37]  Calculated signature: sha256=74f782a7d558636645c043d40ebed055a7ceea6f9a07d326563dba9853287bfc
[webhook] [2025-09-04 07:58:55] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 18bdec4dec33b7dc\n    [X-B3-Traceid] => 68b946bd62749d947e151cf26aebaa21\n    [B3] => 68b946bd62749d947e151cf26aebaa21-18bdec4dec33b7dc-1\n    [Traceparent] => 00-68b946bd62749d947e151cf26aebaa21-18bdec4dec33b7dc-01\n    [X-Amzn-Trace-Id] => Root=1-68b946bd-62749d947e151cf26aebaa21;Parent=18bdec4dec33b7dc;Sampled=1\n    [X-Adsk-Signature] => sha256=74f782a7d558636645c043d40ebed055a7ceea6f9a07d326563dba9853287bfc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dca324fa-965c-4bf3-bc1d-69453310e193\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 07:58:55] [adwsapi_v2.php:57]  Received webhook data: {"id":"dca324fa-965c-4bf3-bc1d-69453310e193","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059389","transactionId":"10d15e62-c469-5d81-aeeb-3f518c9b6f9a","quoteStatus":"Draft","message":"Quote# Q-1059389 status changed to Draft.","modifiedAt":"2025-09-04T07:58:53.161Z"},"publishedAt":"2025-09-04T07:58:53.000Z","csn":"5103159758"}
[webhook] [2025-09-04 07:58:56] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 07:58:56
[webhook] [2025-09-04 07:58:56] [adwsapi_v2.php:36]  Provided signature: sha256=51c67d948c22b2495b01608c131e58a994289f252b956564e279dec502cdf797
[webhook] [2025-09-04 07:58:56] [adwsapi_v2.php:37]  Calculated signature: sha256=74f782a7d558636645c043d40ebed055a7ceea6f9a07d326563dba9853287bfc
[webhook] [2025-09-04 07:58:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cc534a15b7155872\n    [X-B3-Traceid] => 68b946bd62749d947e151cf26aebaa21\n    [B3] => 68b946bd62749d947e151cf26aebaa21-cc534a15b7155872-1\n    [Traceparent] => 00-68b946bd62749d947e151cf26aebaa21-cc534a15b7155872-01\n    [X-Amzn-Trace-Id] => Root=1-68b946bd-62749d947e151cf26aebaa21;Parent=cc534a15b7155872;Sampled=1\n    [X-Adsk-Signature] => sha256=51c67d948c22b2495b01608c131e58a994289f252b956564e279dec502cdf797\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dca324fa-965c-4bf3-bc1d-69453310e193\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 07:58:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"dca324fa-965c-4bf3-bc1d-69453310e193","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059389","transactionId":"10d15e62-c469-5d81-aeeb-3f518c9b6f9a","quoteStatus":"Draft","message":"Quote# Q-1059389 status changed to Draft.","modifiedAt":"2025-09-04T07:58:53.161Z"},"publishedAt":"2025-09-04T07:58:53.000Z","csn":"5103159758"}
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 08:00:02
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:36]  Provided signature: sha256=e5a1fbeee125ac58746d660e5f697bc0497e212ffe5bb5ab700809eb9485b1dd
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:37]  Calculated signature: sha256=e5a1fbeee125ac58746d660e5f697bc0497e212ffe5bb5ab700809eb9485b1dd
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e2121115495010a9\n    [X-B3-Traceid] => 68b9470009b259c0c6197f2b4fc3837d\n    [B3] => 68b9470009b259c0c6197f2b4fc3837d-e2121115495010a9-1\n    [Traceparent] => 00-68b9470009b259c0c6197f2b4fc3837d-e2121115495010a9-01\n    [X-Amzn-Trace-Id] => Root=1-68b94700-09b259c0c6197f2b4fc3837d;Parent=e2121115495010a9;Sampled=1\n    [X-Adsk-Signature] => sha256=e5a1fbeee125ac58746d660e5f697bc0497e212ffe5bb5ab700809eb9485b1dd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => afc55526-41e1-43d4-8848-d8d1d5bb8018\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"afc55526-41e1-43d4-8848-d8d1d5bb8018","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059389","transactionId":"10d15e62-c469-5d81-aeeb-3f518c9b6f9a","quoteStatus":"Quoted","message":"Quote# Q-1059389 status changed to Quoted.","modifiedAt":"2025-09-04T08:00:00.005Z"},"publishedAt":"2025-09-04T08:00:00.000Z","csn":"5103159758"}
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 08:00:02
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:36]  Provided signature: sha256=548b928cf21e09cad11ee8887dea56236af9ca100ea4e7e43bf04f03eda2755d
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:37]  Calculated signature: sha256=e5a1fbeee125ac58746d660e5f697bc0497e212ffe5bb5ab700809eb9485b1dd
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9f821a0677b0d1f3\n    [X-B3-Traceid] => 68b9470009b259c0c6197f2b4fc3837d\n    [B3] => 68b9470009b259c0c6197f2b4fc3837d-9f821a0677b0d1f3-1\n    [Traceparent] => 00-68b9470009b259c0c6197f2b4fc3837d-9f821a0677b0d1f3-01\n    [X-Amzn-Trace-Id] => Root=1-68b94700-09b259c0c6197f2b4fc3837d;Parent=9f821a0677b0d1f3;Sampled=1\n    [X-Adsk-Signature] => sha256=548b928cf21e09cad11ee8887dea56236af9ca100ea4e7e43bf04f03eda2755d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => afc55526-41e1-43d4-8848-d8d1d5bb8018\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 08:00:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"afc55526-41e1-43d4-8848-d8d1d5bb8018","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059389","transactionId":"10d15e62-c469-5d81-aeeb-3f518c9b6f9a","quoteStatus":"Quoted","message":"Quote# Q-1059389 status changed to Quoted.","modifiedAt":"2025-09-04T08:00:00.005Z"},"publishedAt":"2025-09-04T08:00:00.000Z","csn":"5103159758"}
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 08:11:19
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:36]  Provided signature: sha256=95cf9f3da7508b475eea2d7b6ba2849a562b5523e31404b21c52127b81571e93
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:37]  Calculated signature: sha256=10cdbba59ac2d77680beda9ae5a82bc766ce10e85a4ea7ac74dcda19a057ffdb
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 324c0f020b3eb931\n    [X-B3-Traceid] => 68b949a54003ae24126c82137a186e92\n    [B3] => 68b949a54003ae24126c82137a186e92-324c0f020b3eb931-1\n    [Traceparent] => 00-68b949a54003ae24126c82137a186e92-324c0f020b3eb931-01\n    [X-Amzn-Trace-Id] => Root=1-68b949a5-4003ae24126c82137a186e92;Parent=324c0f020b3eb931;Sampled=1\n    [X-Adsk-Signature] => sha256=95cf9f3da7508b475eea2d7b6ba2849a562b5523e31404b21c52127b81571e93\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d0d93d17-c4e4-41ad-af71-d5484ac3df5b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"d0d93d17-c4e4-41ad-af71-d5484ac3df5b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059462","transactionId":"c5af7ee9-5dbb-5794-8787-1fda166e9d24","quoteStatus":"Draft","message":"Quote# Q-1059462 status changed to Draft.","modifiedAt":"2025-09-04T08:11:16.919Z"},"publishedAt":"2025-09-04T08:11:17.000Z","csn":"5103159758"}
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 08:11:19
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:36]  Provided signature: sha256=10cdbba59ac2d77680beda9ae5a82bc766ce10e85a4ea7ac74dcda19a057ffdb
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:37]  Calculated signature: sha256=10cdbba59ac2d77680beda9ae5a82bc766ce10e85a4ea7ac74dcda19a057ffdb
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a271b2bbdc8d2e46\n    [X-B3-Traceid] => 68b949a54003ae24126c82137a186e92\n    [B3] => 68b949a54003ae24126c82137a186e92-a271b2bbdc8d2e46-1\n    [Traceparent] => 00-68b949a54003ae24126c82137a186e92-a271b2bbdc8d2e46-01\n    [X-Amzn-Trace-Id] => Root=1-68b949a5-4003ae24126c82137a186e92;Parent=a271b2bbdc8d2e46;Sampled=1\n    [X-Adsk-Signature] => sha256=10cdbba59ac2d77680beda9ae5a82bc766ce10e85a4ea7ac74dcda19a057ffdb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d0d93d17-c4e4-41ad-af71-d5484ac3df5b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 08:11:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"d0d93d17-c4e4-41ad-af71-d5484ac3df5b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059462","transactionId":"c5af7ee9-5dbb-5794-8787-1fda166e9d24","quoteStatus":"Draft","message":"Quote# Q-1059462 status changed to Draft.","modifiedAt":"2025-09-04T08:11:16.919Z"},"publishedAt":"2025-09-04T08:11:17.000Z","csn":"5103159758"}
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 08:12:58
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:36]  Provided signature: sha256=d88a0f40e8ea4ac31955020f7dc4896d21281026c9528f37642b46fba5a8094f
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:37]  Calculated signature: sha256=d88a0f40e8ea4ac31955020f7dc4896d21281026c9528f37642b46fba5a8094f
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a065d4a1df962b41\n    [X-B3-Traceid] => 68b94a072aacfcfd21be4cea44b5fe2c\n    [B3] => 68b94a072aacfcfd21be4cea44b5fe2c-a065d4a1df962b41-1\n    [Traceparent] => 00-68b94a072aacfcfd21be4cea44b5fe2c-a065d4a1df962b41-01\n    [X-Amzn-Trace-Id] => Root=1-68b94a07-2aacfcfd21be4cea44b5fe2c;Parent=a065d4a1df962b41;Sampled=1\n    [X-Adsk-Signature] => sha256=d88a0f40e8ea4ac31955020f7dc4896d21281026c9528f37642b46fba5a8094f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 14f9c1dc-620c-419f-a586-af4dbba6a7cc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"14f9c1dc-620c-419f-a586-af4dbba6a7cc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059462","transactionId":"c5af7ee9-5dbb-5794-8787-1fda166e9d24","quoteStatus":"Quoted","message":"Quote# Q-1059462 status changed to Quoted.","modifiedAt":"2025-09-04T08:12:55.529Z"},"publishedAt":"2025-09-04T08:12:56.000Z","csn":"5103159758"}
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 08:12:58
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:36]  Provided signature: sha256=79914faa977db50aaccfddb3740efb08db3064f42dbac16e4f980bf02f61df2a
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:37]  Calculated signature: sha256=d88a0f40e8ea4ac31955020f7dc4896d21281026c9528f37642b46fba5a8094f
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 37c099987ac0cddf\n    [X-B3-Traceid] => 68b94a072aacfcfd21be4cea44b5fe2c\n    [B3] => 68b94a072aacfcfd21be4cea44b5fe2c-37c099987ac0cddf-1\n    [Traceparent] => 00-68b94a072aacfcfd21be4cea44b5fe2c-37c099987ac0cddf-01\n    [X-Amzn-Trace-Id] => Root=1-68b94a07-2aacfcfd21be4cea44b5fe2c;Parent=37c099987ac0cddf;Sampled=1\n    [X-Adsk-Signature] => sha256=79914faa977db50aaccfddb3740efb08db3064f42dbac16e4f980bf02f61df2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 14f9c1dc-620c-419f-a586-af4dbba6a7cc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 08:12:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"14f9c1dc-620c-419f-a586-af4dbba6a7cc","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059462","transactionId":"c5af7ee9-5dbb-5794-8787-1fda166e9d24","quoteStatus":"Quoted","message":"Quote# Q-1059462 status changed to Quoted.","modifiedAt":"2025-09-04T08:12:55.529Z"},"publishedAt":"2025-09-04T08:12:56.000Z","csn":"5103159758"}
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 09:07:23
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:36]  Provided signature: sha256=e7dc03fd4ad425ed15104c824ba213ef535f72b66f12e09f32f3fe573f01d287
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:37]  Calculated signature: sha256=5fdb447467f42f8c25a6105ae837140bc6dc484f003e745ac8ac76e7f6d6a02d
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bad1525b5506b6a8\n    [X-B3-Traceid] => 68b956c9b8e6edccd17b583a62a11c1a\n    [B3] => 68b956c9b8e6edccd17b583a62a11c1a-bad1525b5506b6a8-1\n    [Traceparent] => 00-68b956c9b8e6edccd17b583a62a11c1a-bad1525b5506b6a8-01\n    [X-Amzn-Trace-Id] => Root=1-68b956c9-b8e6edccd17b583a62a11c1a;Parent=bad1525b5506b6a8;Sampled=1\n    [X-Adsk-Signature] => sha256=e7dc03fd4ad425ed15104c824ba213ef535f72b66f12e09f32f3fe573f01d287\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ac3a6cc9-0a8b-4ba1-8cd5-f835c63d02ac\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"ac3a6cc9-0a8b-4ba1-8cd5-f835c63d02ac","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059754","transactionId":"a1aec4fe-0822-5920-8bb7-a5cd5123def4","quoteStatus":"Draft","message":"Quote# Q-1059754 status changed to Draft.","modifiedAt":"2025-09-04T09:07:20.939Z"},"publishedAt":"2025-09-04T09:07:21.000Z","csn":"5103159758"}
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 09:07:23
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:36]  Provided signature: sha256=5fdb447467f42f8c25a6105ae837140bc6dc484f003e745ac8ac76e7f6d6a02d
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:37]  Calculated signature: sha256=5fdb447467f42f8c25a6105ae837140bc6dc484f003e745ac8ac76e7f6d6a02d
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bf9e1dedd2d48f84\n    [X-B3-Traceid] => 68b956c9b8e6edccd17b583a62a11c1a\n    [B3] => 68b956c9b8e6edccd17b583a62a11c1a-bf9e1dedd2d48f84-1\n    [Traceparent] => 00-68b956c9b8e6edccd17b583a62a11c1a-bf9e1dedd2d48f84-01\n    [X-Amzn-Trace-Id] => Root=1-68b956c9-b8e6edccd17b583a62a11c1a;Parent=bf9e1dedd2d48f84;Sampled=1\n    [X-Adsk-Signature] => sha256=5fdb447467f42f8c25a6105ae837140bc6dc484f003e745ac8ac76e7f6d6a02d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ac3a6cc9-0a8b-4ba1-8cd5-f835c63d02ac\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 09:07:23] [adwsapi_v2.php:57]  Received webhook data: {"id":"ac3a6cc9-0a8b-4ba1-8cd5-f835c63d02ac","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059754","transactionId":"a1aec4fe-0822-5920-8bb7-a5cd5123def4","quoteStatus":"Draft","message":"Quote# Q-1059754 status changed to Draft.","modifiedAt":"2025-09-04T09:07:20.939Z"},"publishedAt":"2025-09-04T09:07:21.000Z","csn":"5103159758"}
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 09:08:24
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:36]  Provided signature: sha256=1c76abbd27259e2374899258e24bd742f33e9d7f3429d000927bbe92bb57706b
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:37]  Calculated signature: sha256=fbf1c2ee40c20957fbcb0683200aa96131b1d704b849b6769c466d71cd05c1b0
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1b6a6e815881c658\n    [X-B3-Traceid] => 68b95706ddb04f440d0d0ed6268b191f\n    [B3] => 68b95706ddb04f440d0d0ed6268b191f-1b6a6e815881c658-1\n    [Traceparent] => 00-68b95706ddb04f440d0d0ed6268b191f-1b6a6e815881c658-01\n    [X-Amzn-Trace-Id] => Root=1-68b95706-ddb04f440d0d0ed6268b191f;Parent=1b6a6e815881c658;Sampled=1\n    [X-Adsk-Signature] => sha256=1c76abbd27259e2374899258e24bd742f33e9d7f3429d000927bbe92bb57706b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 718530cb-d9ce-43d7-9981-d0575e3b7bcb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"718530cb-d9ce-43d7-9981-d0575e3b7bcb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059754","transactionId":"a1aec4fe-0822-5920-8bb7-a5cd5123def4","quoteStatus":"Quoted","message":"Quote# Q-1059754 status changed to Quoted.","modifiedAt":"2025-09-04T09:08:22.375Z"},"publishedAt":"2025-09-04T09:08:22.000Z","csn":"5103159758"}
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 09:08:24
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:36]  Provided signature: sha256=fbf1c2ee40c20957fbcb0683200aa96131b1d704b849b6769c466d71cd05c1b0
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:37]  Calculated signature: sha256=fbf1c2ee40c20957fbcb0683200aa96131b1d704b849b6769c466d71cd05c1b0
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 63fd0136159b7130\n    [X-B3-Traceid] => 68b95706ddb04f440d0d0ed6268b191f\n    [B3] => 68b95706ddb04f440d0d0ed6268b191f-63fd0136159b7130-1\n    [Traceparent] => 00-68b95706ddb04f440d0d0ed6268b191f-63fd0136159b7130-01\n    [X-Amzn-Trace-Id] => Root=1-68b95706-ddb04f440d0d0ed6268b191f;Parent=63fd0136159b7130;Sampled=1\n    [X-Adsk-Signature] => sha256=fbf1c2ee40c20957fbcb0683200aa96131b1d704b849b6769c466d71cd05c1b0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 718530cb-d9ce-43d7-9981-d0575e3b7bcb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 09:08:24] [adwsapi_v2.php:57]  Received webhook data: {"id":"718530cb-d9ce-43d7-9981-d0575e3b7bcb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1059754","transactionId":"a1aec4fe-0822-5920-8bb7-a5cd5123def4","quoteStatus":"Quoted","message":"Quote# Q-1059754 status changed to Quoted.","modifiedAt":"2025-09-04T09:08:22.375Z"},"publishedAt":"2025-09-04T09:08:22.000Z","csn":"5103159758"}
[webhook] [2025-09-04 10:49:57] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 10:49:57
[webhook] [2025-09-04 10:49:57] [adwsapi_v2.php:36]  Provided signature: sha256=3bd3d461054dce456d335f920d59189c518f4d8018695bcd0525c94ba3001348
[webhook] [2025-09-04 10:49:57] [adwsapi_v2.php:37]  Calculated signature: sha256=3bd3d461054dce456d335f920d59189c518f4d8018695bcd0525c94ba3001348
[webhook] [2025-09-04 10:49:57] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c34d9a3e09bc4106\n    [X-B3-Traceid] => 68b96ed2fa8265c6f6d0e097d7b9b053\n    [B3] => 68b96ed2fa8265c6f6d0e097d7b9b053-c34d9a3e09bc4106-1\n    [Traceparent] => 00-68b96ed2fa8265c6f6d0e097d7b9b053-c34d9a3e09bc4106-01\n    [X-Amzn-Trace-Id] => Root=1-68b96ed2-fa8265c6f6d0e097d7b9b053;Parent=c34d9a3e09bc4106;Sampled=1\n    [X-Adsk-Signature] => sha256=3bd3d461054dce456d335f920d59189c518f4d8018695bcd0525c94ba3001348\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 670d4050-efe6-4f5f-876c-169483a3f91b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 10:49:57] [adwsapi_v2.php:57]  Received webhook data: {"id":"670d4050-efe6-4f5f-876c-169483a3f91b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060325","transactionId":"e43ae601-2a7d-5ec1-a777-87325b6dceb3","quoteStatus":"Draft","message":"Quote# Q-1060325 status changed to Draft.","modifiedAt":"2025-09-04T10:49:54.342Z"},"publishedAt":"2025-09-04T10:49:54.000Z","csn":"5103159758"}
[webhook] [2025-09-04 10:50:01] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 10:50:01
[webhook] [2025-09-04 10:50:01] [adwsapi_v2.php:36]  Provided signature: sha256=d2cf8be650b58b247682cca928fc4459f587b089300fa0711f440dd38dce9705
[webhook] [2025-09-04 10:50:01] [adwsapi_v2.php:37]  Calculated signature: sha256=3bd3d461054dce456d335f920d59189c518f4d8018695bcd0525c94ba3001348
[webhook] [2025-09-04 10:50:01] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 07f1f899c10414e4\n    [X-B3-Traceid] => 68b96ed2fa8265c6f6d0e097d7b9b053\n    [B3] => 68b96ed2fa8265c6f6d0e097d7b9b053-07f1f899c10414e4-1\n    [Traceparent] => 00-68b96ed2fa8265c6f6d0e097d7b9b053-07f1f899c10414e4-01\n    [X-Amzn-Trace-Id] => Root=1-68b96ed2-fa8265c6f6d0e097d7b9b053;Parent=07f1f899c10414e4;Sampled=1\n    [X-Adsk-Signature] => sha256=d2cf8be650b58b247682cca928fc4459f587b089300fa0711f440dd38dce9705\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 670d4050-efe6-4f5f-876c-169483a3f91b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 10:50:01] [adwsapi_v2.php:57]  Received webhook data: {"id":"670d4050-efe6-4f5f-876c-169483a3f91b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060325","transactionId":"e43ae601-2a7d-5ec1-a777-87325b6dceb3","quoteStatus":"Draft","message":"Quote# Q-1060325 status changed to Draft.","modifiedAt":"2025-09-04T10:49:54.342Z"},"publishedAt":"2025-09-04T10:49:54.000Z","csn":"5103159758"}
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 10:50:51
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:36]  Provided signature: sha256=489c754ccd8c6e567d74641c4470acc0a69dd8f2ca9f5248dfac3a181f085e1c
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:37]  Calculated signature: sha256=489c754ccd8c6e567d74641c4470acc0a69dd8f2ca9f5248dfac3a181f085e1c
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9d95559c044431d0\n    [X-B3-Traceid] => 68b96f09ca96cb1a3ed20461ba0fc5a7\n    [B3] => 68b96f09ca96cb1a3ed20461ba0fc5a7-9d95559c044431d0-1\n    [Traceparent] => 00-68b96f09ca96cb1a3ed20461ba0fc5a7-9d95559c044431d0-01\n    [X-Amzn-Trace-Id] => Root=1-68b96f09-ca96cb1a3ed20461ba0fc5a7;Parent=9d95559c044431d0;Sampled=1\n    [X-Adsk-Signature] => sha256=489c754ccd8c6e567d74641c4470acc0a69dd8f2ca9f5248dfac3a181f085e1c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7e25d89b-3332-4026-a360-b51743048433\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"7e25d89b-3332-4026-a360-b51743048433","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060325","transactionId":"e43ae601-2a7d-5ec1-a777-87325b6dceb3","quoteStatus":"Quoted","message":"Quote# Q-1060325 status changed to Quoted.","modifiedAt":"2025-09-04T10:50:48.792Z"},"publishedAt":"2025-09-04T10:50:49.000Z","csn":"5103159758"}
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 10:50:51
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:36]  Provided signature: sha256=6745606813f40673da8e874352f4a5d04f2d4843e95f46127c043724b3cc6efc
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:37]  Calculated signature: sha256=489c754ccd8c6e567d74641c4470acc0a69dd8f2ca9f5248dfac3a181f085e1c
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0098a8e26bfabe0e\n    [X-B3-Traceid] => 68b96f09ca96cb1a3ed20461ba0fc5a7\n    [B3] => 68b96f09ca96cb1a3ed20461ba0fc5a7-0098a8e26bfabe0e-1\n    [Traceparent] => 00-68b96f09ca96cb1a3ed20461ba0fc5a7-0098a8e26bfabe0e-01\n    [X-Amzn-Trace-Id] => Root=1-68b96f09-ca96cb1a3ed20461ba0fc5a7;Parent=0098a8e26bfabe0e;Sampled=1\n    [X-Adsk-Signature] => sha256=6745606813f40673da8e874352f4a5d04f2d4843e95f46127c043724b3cc6efc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7e25d89b-3332-4026-a360-b51743048433\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 10:50:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"7e25d89b-3332-4026-a360-b51743048433","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060325","transactionId":"e43ae601-2a7d-5ec1-a777-87325b6dceb3","quoteStatus":"Quoted","message":"Quote# Q-1060325 status changed to Quoted.","modifiedAt":"2025-09-04T10:50:48.792Z"},"publishedAt":"2025-09-04T10:50:49.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:06:14
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:36]  Provided signature: sha256=e6d8814400c60362ae0ceee6e9f27dce50b77446a61c27a6d567aa926fd68bd3
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:37]  Calculated signature: sha256=e6d8814400c60362ae0ceee6e9f27dce50b77446a61c27a6d567aa926fd68bd3
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8d7e5f5715cf12e6\n    [X-B3-Traceid] => 68b980b433bb8b9518b69f1c0b5c9700\n    [B3] => 68b980b433bb8b9518b69f1c0b5c9700-8d7e5f5715cf12e6-1\n    [Traceparent] => 00-68b980b433bb8b9518b69f1c0b5c9700-8d7e5f5715cf12e6-01\n    [X-Amzn-Trace-Id] => Root=1-68b980b4-33bb8b9518b69f1c0b5c9700;Parent=8d7e5f5715cf12e6;Sampled=1\n    [X-Adsk-Signature] => sha256=e6d8814400c60362ae0ceee6e9f27dce50b77446a61c27a6d567aa926fd68bd3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756987572202-66264904812166-9033769975-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756987572202-66264904812166-9033769975-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66264904812166","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T12:06:12.202Z"},"publishedAt":"2025-09-04T12:06:12.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:06:14
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:36]  Provided signature: sha256=21418df501df5db099d8dd36ab6ad5df02fb2d5839428907af2c6f00cec6ec31
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:37]  Calculated signature: sha256=e6d8814400c60362ae0ceee6e9f27dce50b77446a61c27a6d567aa926fd68bd3
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9cac5ec336002d6f\n    [X-B3-Traceid] => 68b980b433bb8b9518b69f1c0b5c9700\n    [B3] => 68b980b433bb8b9518b69f1c0b5c9700-9cac5ec336002d6f-1\n    [Traceparent] => 00-68b980b433bb8b9518b69f1c0b5c9700-9cac5ec336002d6f-01\n    [X-Amzn-Trace-Id] => Root=1-68b980b4-33bb8b9518b69f1c0b5c9700;Parent=9cac5ec336002d6f;Sampled=1\n    [X-Adsk-Signature] => sha256=21418df501df5db099d8dd36ab6ad5df02fb2d5839428907af2c6f00cec6ec31\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756987572202-66264904812166-9033769975-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:06:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756987572202-66264904812166-9033769975-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66264904812166","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T12:06:12.202Z"},"publishedAt":"2025-09-04T12:06:12.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:12:17
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:36]  Provided signature: sha256=48b7c6a6e383a3861a4c63cc6034f37f78034ca3f9e0bc1669d2f1db4b0438a8
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:37]  Calculated signature: sha256=5a179e484051624ef2f2a9602fc81df6ebbb7e522b26f4af91780c40ad46920f
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b3c7d062afca801a\n    [X-B3-Traceid] => 68b9821f72a01f436de23123336f2664\n    [B3] => 68b9821f72a01f436de23123336f2664-b3c7d062afca801a-1\n    [Traceparent] => 00-68b9821f72a01f436de23123336f2664-b3c7d062afca801a-01\n    [X-Amzn-Trace-Id] => Root=1-68b9821f-72a01f436de23123336f2664;Parent=b3c7d062afca801a;Sampled=1\n    [X-Adsk-Signature] => sha256=48b7c6a6e383a3861a4c63cc6034f37f78034ca3f9e0bc1669d2f1db4b0438a8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756987935109-66187025913229-9033816003-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756987935109-66187025913229-9033816003-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66187025913229","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T12:12:15.109Z"},"publishedAt":"2025-09-04T12:12:15.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:12:17
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:36]  Provided signature: sha256=5a179e484051624ef2f2a9602fc81df6ebbb7e522b26f4af91780c40ad46920f
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:37]  Calculated signature: sha256=5a179e484051624ef2f2a9602fc81df6ebbb7e522b26f4af91780c40ad46920f
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1f6e0ea6eed56ed6\n    [X-B3-Traceid] => 68b9821f72a01f436de23123336f2664\n    [B3] => 68b9821f72a01f436de23123336f2664-1f6e0ea6eed56ed6-1\n    [Traceparent] => 00-68b9821f72a01f436de23123336f2664-1f6e0ea6eed56ed6-01\n    [X-Amzn-Trace-Id] => Root=1-68b9821f-72a01f436de23123336f2664;Parent=1f6e0ea6eed56ed6;Sampled=1\n    [X-Adsk-Signature] => sha256=5a179e484051624ef2f2a9602fc81df6ebbb7e522b26f4af91780c40ad46920f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1756987935109-66187025913229-9033816003-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:12:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"1756987935109-66187025913229-9033816003-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66187025913229","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T12:12:15.109Z"},"publishedAt":"2025-09-04T12:12:15.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:24:10
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:36]  Provided signature: sha256=3e89e827cbd86d45f69ef5b8eacff8a1f1c2bfbce863939d0a257a896cd580af
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:37]  Calculated signature: sha256=645e8cb982a53470c4c4f1ab539e93fdb9fc07e15276fdc022f3d9ca6c03ab53
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c9531e9026ccb92a\n    [X-B3-Traceid] => 68b984e8383eade081930f9910e487eb\n    [B3] => 68b984e8383eade081930f9910e487eb-c9531e9026ccb92a-1\n    [Traceparent] => 00-68b984e8383eade081930f9910e487eb-c9531e9026ccb92a-01\n    [X-Amzn-Trace-Id] => Root=1-68b984e8-383eade081930f9910e487eb;Parent=c9531e9026ccb92a;Sampled=1\n    [X-Adsk-Signature] => sha256=3e89e827cbd86d45f69ef5b8eacff8a1f1c2bfbce863939d0a257a896cd580af\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1b50bd72-02a8-4f01-908f-111bd47a3be8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"1b50bd72-02a8-4f01-908f-111bd47a3be8","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060637","transactionId":"d1ec2463-5045-5998-8869-4550d7643639","quoteStatus":"Draft","message":"Quote# Q-1060637 status changed to Draft.","modifiedAt":"2025-09-04T12:24:08.071Z"},"publishedAt":"2025-09-04T12:24:08.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:24:10
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:36]  Provided signature: sha256=645e8cb982a53470c4c4f1ab539e93fdb9fc07e15276fdc022f3d9ca6c03ab53
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:37]  Calculated signature: sha256=645e8cb982a53470c4c4f1ab539e93fdb9fc07e15276fdc022f3d9ca6c03ab53
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0189fbc6cbbe145c\n    [X-B3-Traceid] => 68b984e8383eade081930f9910e487eb\n    [B3] => 68b984e8383eade081930f9910e487eb-0189fbc6cbbe145c-1\n    [Traceparent] => 00-68b984e8383eade081930f9910e487eb-0189fbc6cbbe145c-01\n    [X-Amzn-Trace-Id] => Root=1-68b984e8-383eade081930f9910e487eb;Parent=0189fbc6cbbe145c;Sampled=1\n    [X-Adsk-Signature] => sha256=645e8cb982a53470c4c4f1ab539e93fdb9fc07e15276fdc022f3d9ca6c03ab53\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1b50bd72-02a8-4f01-908f-111bd47a3be8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:24:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"1b50bd72-02a8-4f01-908f-111bd47a3be8","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060637","transactionId":"d1ec2463-5045-5998-8869-4550d7643639","quoteStatus":"Draft","message":"Quote# Q-1060637 status changed to Draft.","modifiedAt":"2025-09-04T12:24:08.071Z"},"publishedAt":"2025-09-04T12:24:08.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:24:50
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:36]  Provided signature: sha256=2ef10ed4a350c7f96885e44c3c7c9f3e90b110d70d484d9cc2e1a7c801f6dda1
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:37]  Calculated signature: sha256=c6f4cc5bd7d49941c366bc5a7b5424828d27d3f5c6ffb784bca47d0af2681fcb
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f4c014a01f8956e0\n    [X-B3-Traceid] => 68b9850f8a0b6752bb7cbb94530f12e7\n    [B3] => 68b9850f8a0b6752bb7cbb94530f12e7-f4c014a01f8956e0-1\n    [Traceparent] => 00-68b9850f8a0b6752bb7cbb94530f12e7-f4c014a01f8956e0-01\n    [X-Amzn-Trace-Id] => Root=1-68b9850f-8a0b6752bb7cbb94530f12e7;Parent=f4c014a01f8956e0;Sampled=1\n    [X-Adsk-Signature] => sha256=2ef10ed4a350c7f96885e44c3c7c9f3e90b110d70d484d9cc2e1a7c801f6dda1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ca4c56e5-2413-481f-a279-1c22ef83af6e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"ca4c56e5-2413-481f-a279-1c22ef83af6e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060637","transactionId":"d1ec2463-5045-5998-8869-4550d7643639","quoteStatus":"Quoted","message":"Quote# Q-1060637 status changed to Quoted.","modifiedAt":"2025-09-04T12:24:47.695Z"},"publishedAt":"2025-09-04T12:24:47.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:24:50
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:36]  Provided signature: sha256=c6f4cc5bd7d49941c366bc5a7b5424828d27d3f5c6ffb784bca47d0af2681fcb
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:37]  Calculated signature: sha256=c6f4cc5bd7d49941c366bc5a7b5424828d27d3f5c6ffb784bca47d0af2681fcb
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 614105ed4546769b\n    [X-B3-Traceid] => 68b9850f8a0b6752bb7cbb94530f12e7\n    [B3] => 68b9850f8a0b6752bb7cbb94530f12e7-614105ed4546769b-1\n    [Traceparent] => 00-68b9850f8a0b6752bb7cbb94530f12e7-614105ed4546769b-01\n    [X-Amzn-Trace-Id] => Root=1-68b9850f-8a0b6752bb7cbb94530f12e7;Parent=614105ed4546769b;Sampled=1\n    [X-Adsk-Signature] => sha256=c6f4cc5bd7d49941c366bc5a7b5424828d27d3f5c6ffb784bca47d0af2681fcb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ca4c56e5-2413-481f-a279-1c22ef83af6e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:24:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"ca4c56e5-2413-481f-a279-1c22ef83af6e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060637","transactionId":"d1ec2463-5045-5998-8869-4550d7643639","quoteStatus":"Quoted","message":"Quote# Q-1060637 status changed to Quoted.","modifiedAt":"2025-09-04T12:24:47.695Z"},"publishedAt":"2025-09-04T12:24:47.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:30:15
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:36]  Provided signature: sha256=d0ca8def17668b1d077a40e64c5ac220dabd96a1c712591c08f486e229cd9a66
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:37]  Calculated signature: sha256=d4556faae4636986d16556debaaaf0ca1b1839077062041984df51f9e1be5b72
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 17ee7f6a14f6512b\n    [X-B3-Traceid] => 68b98654b80588ca107fe0b0e9067f36\n    [B3] => 68b98654b80588ca107fe0b0e9067f36-17ee7f6a14f6512b-1\n    [Traceparent] => 00-68b98654b80588ca107fe0b0e9067f36-17ee7f6a14f6512b-01\n    [X-Amzn-Trace-Id] => Root=1-68b98654-b80588ca107fe0b0e9067f36;Parent=17ee7f6a14f6512b;Sampled=1\n    [X-Adsk-Signature] => sha256=d0ca8def17668b1d077a40e64c5ac220dabd96a1c712591c08f486e229cd9a66\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 85f42dc0-3d2e-4757-ad94-7e2b830523cf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"85f42dc0-3d2e-4757-ad94-7e2b830523cf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060665","transactionId":"fc4dc41c-f182-53c8-82f9-a52f7912aeeb","quoteStatus":"Draft","message":"Quote# Q-1060665 status changed to Draft.","modifiedAt":"2025-09-04T12:30:12.421Z"},"publishedAt":"2025-09-04T12:30:12.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:30:15
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:36]  Provided signature: sha256=d4556faae4636986d16556debaaaf0ca1b1839077062041984df51f9e1be5b72
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:37]  Calculated signature: sha256=d4556faae4636986d16556debaaaf0ca1b1839077062041984df51f9e1be5b72
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 94277060c4b46bec\n    [X-B3-Traceid] => 68b98654b80588ca107fe0b0e9067f36\n    [B3] => 68b98654b80588ca107fe0b0e9067f36-94277060c4b46bec-1\n    [Traceparent] => 00-68b98654b80588ca107fe0b0e9067f36-94277060c4b46bec-01\n    [X-Amzn-Trace-Id] => Root=1-68b98654-b80588ca107fe0b0e9067f36;Parent=94277060c4b46bec;Sampled=1\n    [X-Adsk-Signature] => sha256=d4556faae4636986d16556debaaaf0ca1b1839077062041984df51f9e1be5b72\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 85f42dc0-3d2e-4757-ad94-7e2b830523cf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:30:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"85f42dc0-3d2e-4757-ad94-7e2b830523cf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060665","transactionId":"fc4dc41c-f182-53c8-82f9-a52f7912aeeb","quoteStatus":"Draft","message":"Quote# Q-1060665 status changed to Draft.","modifiedAt":"2025-09-04T12:30:12.421Z"},"publishedAt":"2025-09-04T12:30:12.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:30:50] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:30:50
[webhook] [2025-09-04 12:30:50] [adwsapi_v2.php:36]  Provided signature: sha256=6475dc9dc4884c666828d7f34ef95928c4ffc586eed77dc6bb6564b0452f3cf3
[webhook] [2025-09-04 12:30:50] [adwsapi_v2.php:37]  Calculated signature: sha256=6475dc9dc4884c666828d7f34ef95928c4ffc586eed77dc6bb6564b0452f3cf3
[webhook] [2025-09-04 12:30:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 63ff0fc8a83e924b\n    [X-B3-Traceid] => 68b98678f3e3768c19e2a96bd6d96120\n    [B3] => 68b98678f3e3768c19e2a96bd6d96120-63ff0fc8a83e924b-1\n    [Traceparent] => 00-68b98678f3e3768c19e2a96bd6d96120-63ff0fc8a83e924b-01\n    [X-Amzn-Trace-Id] => Root=1-68b98678-f3e3768c19e2a96bd6d96120;Parent=63ff0fc8a83e924b;Sampled=1\n    [X-Adsk-Signature] => sha256=6475dc9dc4884c666828d7f34ef95928c4ffc586eed77dc6bb6564b0452f3cf3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 052ef159-18ea-434a-af24-35ed7cab00e8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:30:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"052ef159-18ea-434a-af24-35ed7cab00e8","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060665","transactionId":"fc4dc41c-f182-53c8-82f9-a52f7912aeeb","quoteStatus":"Quoted","message":"Quote# Q-1060665 status changed to Quoted.","modifiedAt":"2025-09-04T12:30:48.240Z"},"publishedAt":"2025-09-04T12:30:48.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:30:51] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:30:51
[webhook] [2025-09-04 12:30:51] [adwsapi_v2.php:36]  Provided signature: sha256=d7c00c50273c15164c50731fecca9dd3a252bbb3b907e108eb421fff1b7e1710
[webhook] [2025-09-04 12:30:51] [adwsapi_v2.php:37]  Calculated signature: sha256=6475dc9dc4884c666828d7f34ef95928c4ffc586eed77dc6bb6564b0452f3cf3
[webhook] [2025-09-04 12:30:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 111aea6b6d6d08b2\n    [X-B3-Traceid] => 68b98678f3e3768c19e2a96bd6d96120\n    [B3] => 68b98678f3e3768c19e2a96bd6d96120-111aea6b6d6d08b2-1\n    [Traceparent] => 00-68b98678f3e3768c19e2a96bd6d96120-111aea6b6d6d08b2-01\n    [X-Amzn-Trace-Id] => Root=1-68b98678-f3e3768c19e2a96bd6d96120;Parent=111aea6b6d6d08b2;Sampled=1\n    [X-Adsk-Signature] => sha256=d7c00c50273c15164c50731fecca9dd3a252bbb3b907e108eb421fff1b7e1710\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 052ef159-18ea-434a-af24-35ed7cab00e8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:30:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"052ef159-18ea-434a-af24-35ed7cab00e8","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060665","transactionId":"fc4dc41c-f182-53c8-82f9-a52f7912aeeb","quoteStatus":"Quoted","message":"Quote# Q-1060665 status changed to Quoted.","modifiedAt":"2025-09-04T12:30:48.240Z"},"publishedAt":"2025-09-04T12:30:48.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:37:49
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:36]  Provided signature: sha256=1c76904fa4c622121c6eef689a93ac9b79103a17b25460a75f349971db2566fb
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:37]  Calculated signature: sha256=1c76904fa4c622121c6eef689a93ac9b79103a17b25460a75f349971db2566fb
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d0982758fb871979\n    [X-B3-Traceid] => 68b9881b4e15ddd3945ccf9aedec3003\n    [B3] => 68b9881b4e15ddd3945ccf9aedec3003-d0982758fb871979-1\n    [Traceparent] => 00-68b9881b4e15ddd3945ccf9aedec3003-d0982758fb871979-01\n    [X-Amzn-Trace-Id] => Root=1-68b9881b-4e15ddd3945ccf9aedec3003;Parent=d0982758fb871979;Sampled=1\n    [X-Adsk-Signature] => sha256=1c76904fa4c622121c6eef689a93ac9b79103a17b25460a75f349971db2566fb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f8796372-74a9-44cd-ae5d-663a41b06214\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"f8796372-74a9-44cd-ae5d-663a41b06214","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060704","transactionId":"b9f1d72c-632f-5629-8e4d-b61fe97e7f72","quoteStatus":"Draft","message":"Quote# Q-1060704 status changed to Draft.","modifiedAt":"2025-09-04T12:37:47.499Z"},"publishedAt":"2025-09-04T12:37:47.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:37:49
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:36]  Provided signature: sha256=040c37fbccbb2b40f1156917dec1dcafbc95b3a8aaa49358463328ff79064e2a
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:37]  Calculated signature: sha256=1c76904fa4c622121c6eef689a93ac9b79103a17b25460a75f349971db2566fb
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bf49bf913459fe6d\n    [X-B3-Traceid] => 68b9881b4e15ddd3945ccf9aedec3003\n    [B3] => 68b9881b4e15ddd3945ccf9aedec3003-bf49bf913459fe6d-1\n    [Traceparent] => 00-68b9881b4e15ddd3945ccf9aedec3003-bf49bf913459fe6d-01\n    [X-Amzn-Trace-Id] => Root=1-68b9881b-4e15ddd3945ccf9aedec3003;Parent=bf49bf913459fe6d;Sampled=1\n    [X-Adsk-Signature] => sha256=040c37fbccbb2b40f1156917dec1dcafbc95b3a8aaa49358463328ff79064e2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f8796372-74a9-44cd-ae5d-663a41b06214\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:37:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"f8796372-74a9-44cd-ae5d-663a41b06214","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060704","transactionId":"b9f1d72c-632f-5629-8e4d-b61fe97e7f72","quoteStatus":"Draft","message":"Quote# Q-1060704 status changed to Draft.","modifiedAt":"2025-09-04T12:37:47.499Z"},"publishedAt":"2025-09-04T12:37:47.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:39:02
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:36]  Provided signature: sha256=acfec38e172b8aae27681ac12637c8e09ca070502cf52c7ae080b7d3e7fa59fa
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:37]  Calculated signature: sha256=acfec38e172b8aae27681ac12637c8e09ca070502cf52c7ae080b7d3e7fa59fa
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 311cfb247d82ff97\n    [X-B3-Traceid] => 68b98863c5d8f543c0ea55b5bedaada3\n    [B3] => 68b98863c5d8f543c0ea55b5bedaada3-311cfb247d82ff97-1\n    [Traceparent] => 00-68b98863c5d8f543c0ea55b5bedaada3-311cfb247d82ff97-01\n    [X-Amzn-Trace-Id] => Root=1-68b98863-c5d8f543c0ea55b5bedaada3;Parent=311cfb247d82ff97;Sampled=1\n    [X-Adsk-Signature] => sha256=acfec38e172b8aae27681ac12637c8e09ca070502cf52c7ae080b7d3e7fa59fa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d901877a-adc0-48ff-9d8e-f94ab5e17b93\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"d901877a-adc0-48ff-9d8e-f94ab5e17b93","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1055070","transactionId":"e49bfa01-7172-5b26-ac83-9b308b32f666","quoteStatus":"Order Submitted","message":"Quote# Q-1055070 status changed to Order Submitted.","modifiedAt":"2025-09-04T12:38:59.361Z"},"publishedAt":"2025-09-04T12:38:59.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:39:02
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:36]  Provided signature: sha256=93a92bb275ca7b90c8c0014bf39af98a11467fe1c99de64a24f3c7439065fe23
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:37]  Calculated signature: sha256=acfec38e172b8aae27681ac12637c8e09ca070502cf52c7ae080b7d3e7fa59fa
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bc3ceac171d927ce\n    [X-B3-Traceid] => 68b98863c5d8f543c0ea55b5bedaada3\n    [B3] => 68b98863c5d8f543c0ea55b5bedaada3-bc3ceac171d927ce-1\n    [Traceparent] => 00-68b98863c5d8f543c0ea55b5bedaada3-bc3ceac171d927ce-01\n    [X-Amzn-Trace-Id] => Root=1-68b98863-c5d8f543c0ea55b5bedaada3;Parent=bc3ceac171d927ce;Sampled=1\n    [X-Adsk-Signature] => sha256=93a92bb275ca7b90c8c0014bf39af98a11467fe1c99de64a24f3c7439065fe23\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d901877a-adc0-48ff-9d8e-f94ab5e17b93\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:39:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"d901877a-adc0-48ff-9d8e-f94ab5e17b93","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1055070","transactionId":"e49bfa01-7172-5b26-ac83-9b308b32f666","quoteStatus":"Order Submitted","message":"Quote# Q-1055070 status changed to Order Submitted.","modifiedAt":"2025-09-04T12:38:59.361Z"},"publishedAt":"2025-09-04T12:38:59.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:39:06
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:36]  Provided signature: sha256=0e2ebd8a27e42f8a6c0086bce5885d01103adc59977373489fd3f546734cff3a
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:37]  Calculated signature: sha256=bca63612e528a4362b59615c5f865d1bb41709e90e21ab223bb2856416e44ecc
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3ab8f363b9e2e5f3\n    [X-B3-Traceid] => 68b98868e8b251def7659a4f4360c4e4\n    [B3] => 68b98868e8b251def7659a4f4360c4e4-3ab8f363b9e2e5f3-1\n    [Traceparent] => 00-68b98868e8b251def7659a4f4360c4e4-3ab8f363b9e2e5f3-01\n    [X-Amzn-Trace-Id] => Root=1-68b98868-e8b251def7659a4f4360c4e4;Parent=3ab8f363b9e2e5f3;Sampled=1\n    [X-Adsk-Signature] => sha256=0e2ebd8a27e42f8a6c0086bce5885d01103adc59977373489fd3f546734cff3a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4c2ef236-6c67-40aa-9aae-724cd83534a6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"4c2ef236-6c67-40aa-9aae-724cd83534a6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1055070","transactionId":"e49bfa01-7172-5b26-ac83-9b308b32f666","quoteStatus":"Ordered","message":"Quote# Q-1055070 status changed to Ordered.","modifiedAt":"2025-09-04T12:39:04.069Z"},"publishedAt":"2025-09-04T12:39:04.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:39:06
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:36]  Provided signature: sha256=bca63612e528a4362b59615c5f865d1bb41709e90e21ab223bb2856416e44ecc
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:37]  Calculated signature: sha256=bca63612e528a4362b59615c5f865d1bb41709e90e21ab223bb2856416e44ecc
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5d264e4ab00f4be0\n    [X-B3-Traceid] => 68b98868e8b251def7659a4f4360c4e4\n    [B3] => 68b98868e8b251def7659a4f4360c4e4-5d264e4ab00f4be0-1\n    [Traceparent] => 00-68b98868e8b251def7659a4f4360c4e4-5d264e4ab00f4be0-01\n    [X-Amzn-Trace-Id] => Root=1-68b98868-e8b251def7659a4f4360c4e4;Parent=5d264e4ab00f4be0;Sampled=1\n    [X-Adsk-Signature] => sha256=bca63612e528a4362b59615c5f865d1bb41709e90e21ab223bb2856416e44ecc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4c2ef236-6c67-40aa-9aae-724cd83534a6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:39:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"4c2ef236-6c67-40aa-9aae-724cd83534a6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1055070","transactionId":"e49bfa01-7172-5b26-ac83-9b308b32f666","quoteStatus":"Ordered","message":"Quote# Q-1055070 status changed to Ordered.","modifiedAt":"2025-09-04T12:39:04.069Z"},"publishedAt":"2025-09-04T12:39:04.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:47:14
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:36]  Provided signature: sha256=0dd626972dc69f08e0d1986aab71b7dfd1c21581ecfbf78c5e627121c8f5623d
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:37]  Calculated signature: sha256=791dac7b12dd129923e70c5dc7bbb9bb0ac2172a95e1778b3be6ef0a31ac4bf8
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d7b98b720f05e793\n    [X-B3-Traceid] => 68b98a4f29ac096f19fe7281ed5e8220\n    [B3] => 68b98a4f29ac096f19fe7281ed5e8220-d7b98b720f05e793-1\n    [Traceparent] => 00-68b98a4f29ac096f19fe7281ed5e8220-d7b98b720f05e793-01\n    [X-Amzn-Trace-Id] => Root=1-68b98a4f-29ac096f19fe7281ed5e8220;Parent=d7b98b720f05e793;Sampled=1\n    [X-Adsk-Signature] => sha256=0dd626972dc69f08e0d1986aab71b7dfd1c21581ecfbf78c5e627121c8f5623d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 470bd65c-d765-44e8-a573-4658fa992015\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"470bd65c-d765-44e8-a573-4658fa992015","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060745","transactionId":"d420f78a-fd03-5343-9ef9-96b9717f82b8","quoteStatus":"Draft","message":"Quote# Q-1060745 status changed to Draft.","modifiedAt":"2025-09-04T12:47:11.129Z"},"publishedAt":"2025-09-04T12:47:11.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:47:14
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:36]  Provided signature: sha256=791dac7b12dd129923e70c5dc7bbb9bb0ac2172a95e1778b3be6ef0a31ac4bf8
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:37]  Calculated signature: sha256=791dac7b12dd129923e70c5dc7bbb9bb0ac2172a95e1778b3be6ef0a31ac4bf8
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d61ef16f8492d391\n    [X-B3-Traceid] => 68b98a4f29ac096f19fe7281ed5e8220\n    [B3] => 68b98a4f29ac096f19fe7281ed5e8220-d61ef16f8492d391-1\n    [Traceparent] => 00-68b98a4f29ac096f19fe7281ed5e8220-d61ef16f8492d391-01\n    [X-Amzn-Trace-Id] => Root=1-68b98a4f-29ac096f19fe7281ed5e8220;Parent=d61ef16f8492d391;Sampled=1\n    [X-Adsk-Signature] => sha256=791dac7b12dd129923e70c5dc7bbb9bb0ac2172a95e1778b3be6ef0a31ac4bf8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 470bd65c-d765-44e8-a573-4658fa992015\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:47:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"470bd65c-d765-44e8-a573-4658fa992015","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060745","transactionId":"d420f78a-fd03-5343-9ef9-96b9717f82b8","quoteStatus":"Draft","message":"Quote# Q-1060745 status changed to Draft.","modifiedAt":"2025-09-04T12:47:11.129Z"},"publishedAt":"2025-09-04T12:47:11.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:48:46
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:36]  Provided signature: sha256=427495f57a2c1f0b10bcbc94acf7a924d21c93bbf8894e3899431ca65b683b3d
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:37]  Calculated signature: sha256=9635e22700a46785ee12e792c9b3e968dfb0540c85479efca96666037f7c3da3
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 153d6cd01c1cbcdd\n    [X-B3-Traceid] => 68b98aac988c310c3d3cd191ffde487a\n    [B3] => 68b98aac988c310c3d3cd191ffde487a-153d6cd01c1cbcdd-1\n    [Traceparent] => 00-68b98aac988c310c3d3cd191ffde487a-153d6cd01c1cbcdd-01\n    [X-Amzn-Trace-Id] => Root=1-68b98aac-988c310c3d3cd191ffde487a;Parent=153d6cd01c1cbcdd;Sampled=1\n    [X-Adsk-Signature] => sha256=427495f57a2c1f0b10bcbc94acf7a924d21c93bbf8894e3899431ca65b683b3d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 929685da-ced2-425f-8797-f1fe122fb692\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"929685da-ced2-425f-8797-f1fe122fb692","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060745","transactionId":"d420f78a-fd03-5343-9ef9-96b9717f82b8","quoteStatus":"Quoted","message":"Quote# Q-1060745 status changed to Quoted.","modifiedAt":"2025-09-04T12:48:43.899Z"},"publishedAt":"2025-09-04T12:48:44.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:48:46
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:36]  Provided signature: sha256=9635e22700a46785ee12e792c9b3e968dfb0540c85479efca96666037f7c3da3
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:37]  Calculated signature: sha256=9635e22700a46785ee12e792c9b3e968dfb0540c85479efca96666037f7c3da3
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 48807a1b030d5b5a\n    [X-B3-Traceid] => 68b98aac988c310c3d3cd191ffde487a\n    [B3] => 68b98aac988c310c3d3cd191ffde487a-48807a1b030d5b5a-1\n    [Traceparent] => 00-68b98aac988c310c3d3cd191ffde487a-48807a1b030d5b5a-01\n    [X-Amzn-Trace-Id] => Root=1-68b98aac-988c310c3d3cd191ffde487a;Parent=48807a1b030d5b5a;Sampled=1\n    [X-Adsk-Signature] => sha256=9635e22700a46785ee12e792c9b3e968dfb0540c85479efca96666037f7c3da3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 929685da-ced2-425f-8797-f1fe122fb692\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:48:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"929685da-ced2-425f-8797-f1fe122fb692","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060745","transactionId":"d420f78a-fd03-5343-9ef9-96b9717f82b8","quoteStatus":"Quoted","message":"Quote# Q-1060745 status changed to Quoted.","modifiedAt":"2025-09-04T12:48:43.899Z"},"publishedAt":"2025-09-04T12:48:44.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:48:47
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:36]  Provided signature: sha256=c0202178472336d05f9dc01e0bc9b12cf3b2ad4dc4bcd28861048c4b04987a6d
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:37]  Calculated signature: sha256=c0202178472336d05f9dc01e0bc9b12cf3b2ad4dc4bcd28861048c4b04987a6d
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b269aa0099e2831a\n    [X-B3-Traceid] => 68b98aacb30c260c0fbdf8ee206c7120\n    [B3] => 68b98aacb30c260c0fbdf8ee206c7120-b269aa0099e2831a-1\n    [Traceparent] => 00-68b98aacb30c260c0fbdf8ee206c7120-b269aa0099e2831a-01\n    [X-Amzn-Trace-Id] => Root=1-68b98aac-b30c260c0fbdf8ee206c7120;Parent=b269aa0099e2831a;Sampled=1\n    [X-Adsk-Signature] => sha256=c0202178472336d05f9dc01e0bc9b12cf3b2ad4dc4bcd28861048c4b04987a6d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bd2e9c70-f682-4e6b-bb16-52caef645be1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"bd2e9c70-f682-4e6b-bb16-52caef645be1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060752","transactionId":"60c26524-bd09-5aa4-a8d4-041731d34258","quoteStatus":"Draft","message":"Quote# Q-1060752 status changed to Draft.","modifiedAt":"2025-09-04T12:48:44.090Z"},"publishedAt":"2025-09-04T12:48:44.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:48:47
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:36]  Provided signature: sha256=9431ee012c0b3d2938ae5d7357fd3fa8f273ce4615db0f227a9f090e22328586
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:37]  Calculated signature: sha256=c0202178472336d05f9dc01e0bc9b12cf3b2ad4dc4bcd28861048c4b04987a6d
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ff8453ff9eb1dbcd\n    [X-B3-Traceid] => 68b98aacb30c260c0fbdf8ee206c7120\n    [B3] => 68b98aacb30c260c0fbdf8ee206c7120-ff8453ff9eb1dbcd-1\n    [Traceparent] => 00-68b98aacb30c260c0fbdf8ee206c7120-ff8453ff9eb1dbcd-01\n    [X-Amzn-Trace-Id] => Root=1-68b98aac-b30c260c0fbdf8ee206c7120;Parent=ff8453ff9eb1dbcd;Sampled=1\n    [X-Adsk-Signature] => sha256=9431ee012c0b3d2938ae5d7357fd3fa8f273ce4615db0f227a9f090e22328586\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bd2e9c70-f682-4e6b-bb16-52caef645be1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:48:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"bd2e9c70-f682-4e6b-bb16-52caef645be1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060752","transactionId":"60c26524-bd09-5aa4-a8d4-041731d34258","quoteStatus":"Draft","message":"Quote# Q-1060752 status changed to Draft.","modifiedAt":"2025-09-04T12:48:44.090Z"},"publishedAt":"2025-09-04T12:48:44.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:50:04
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:36]  Provided signature: sha256=c0cad5701dc37b786a697e3dde92a477254579dbe77330476efa1a75b0fefc2d
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:37]  Calculated signature: sha256=8ed25d1f801cff76f40fed94f09c400dd412fe8a8f90a17c719fb9d0890b83e1
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0e82543ee40c93a2\n    [X-B3-Traceid] => 68b98afa741554ee4c4933bcbad01c9f\n    [B3] => 68b98afa741554ee4c4933bcbad01c9f-0e82543ee40c93a2-1\n    [Traceparent] => 00-68b98afa741554ee4c4933bcbad01c9f-0e82543ee40c93a2-01\n    [X-Amzn-Trace-Id] => Root=1-68b98afa-741554ee4c4933bcbad01c9f;Parent=0e82543ee40c93a2;Sampled=1\n    [X-Adsk-Signature] => sha256=c0cad5701dc37b786a697e3dde92a477254579dbe77330476efa1a75b0fefc2d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d6731a12-453a-4133-8c17-a06719c9c570\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"d6731a12-453a-4133-8c17-a06719c9c570","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060752","transactionId":"60c26524-bd09-5aa4-a8d4-041731d34258","quoteStatus":"Quoted","message":"Quote# Q-1060752 status changed to Quoted.","modifiedAt":"2025-09-04T12:50:01.564Z"},"publishedAt":"2025-09-04T12:50:02.000Z","csn":"5103159758"}
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 12:50:04
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:36]  Provided signature: sha256=8ed25d1f801cff76f40fed94f09c400dd412fe8a8f90a17c719fb9d0890b83e1
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:37]  Calculated signature: sha256=8ed25d1f801cff76f40fed94f09c400dd412fe8a8f90a17c719fb9d0890b83e1
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d56f88160d25d926\n    [X-B3-Traceid] => 68b98afa741554ee4c4933bcbad01c9f\n    [B3] => 68b98afa741554ee4c4933bcbad01c9f-d56f88160d25d926-1\n    [Traceparent] => 00-68b98afa741554ee4c4933bcbad01c9f-d56f88160d25d926-01\n    [X-Amzn-Trace-Id] => Root=1-68b98afa-741554ee4c4933bcbad01c9f;Parent=d56f88160d25d926;Sampled=1\n    [X-Adsk-Signature] => sha256=8ed25d1f801cff76f40fed94f09c400dd412fe8a8f90a17c719fb9d0890b83e1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d6731a12-453a-4133-8c17-a06719c9c570\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 12:50:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"d6731a12-453a-4133-8c17-a06719c9c570","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060752","transactionId":"60c26524-bd09-5aa4-a8d4-041731d34258","quoteStatus":"Quoted","message":"Quote# Q-1060752 status changed to Quoted.","modifiedAt":"2025-09-04T12:50:01.564Z"},"publishedAt":"2025-09-04T12:50:02.000Z","csn":"5103159758"}
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 13:07:56
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:36]  Provided signature: sha256=efddc020468b908a3b465192f117ef995c6e0d9d68fda791f8eb457c9d3ae361
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:37]  Calculated signature: sha256=6462f044e00de765f72234d7722e0451adcebc87757e5fdc868f14fd45b6a62e
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4913152ab136b5cd\n    [X-B3-Traceid] => 68b98f2ae2d756db2366d612100e1cdd\n    [B3] => 68b98f2ae2d756db2366d612100e1cdd-4913152ab136b5cd-1\n    [Traceparent] => 00-68b98f2ae2d756db2366d612100e1cdd-4913152ab136b5cd-01\n    [X-Amzn-Trace-Id] => Root=1-68b98f2a-e2d756db2366d612100e1cdd;Parent=4913152ab136b5cd;Sampled=1\n    [X-Adsk-Signature] => sha256=efddc020468b908a3b465192f117ef995c6e0d9d68fda791f8eb457c9d3ae361\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 63e698ff-de20-4b93-b32b-c15e959d9e89\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"63e698ff-de20-4b93-b32b-c15e959d9e89","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1018933","transactionId":"b9acd212-0136-5bbb-8197-b30d876e92ec","quoteStatus":"Order Submitted","message":"Quote# Q-1018933 status changed to Order Submitted.","modifiedAt":"2025-09-04T13:07:54.097Z"},"publishedAt":"2025-09-04T13:07:54.000Z","csn":"5103159758"}
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 13:07:56
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:36]  Provided signature: sha256=6462f044e00de765f72234d7722e0451adcebc87757e5fdc868f14fd45b6a62e
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:37]  Calculated signature: sha256=6462f044e00de765f72234d7722e0451adcebc87757e5fdc868f14fd45b6a62e
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1b0ea71171b0a03b\n    [X-B3-Traceid] => 68b98f2ae2d756db2366d612100e1cdd\n    [B3] => 68b98f2ae2d756db2366d612100e1cdd-1b0ea71171b0a03b-1\n    [Traceparent] => 00-68b98f2ae2d756db2366d612100e1cdd-1b0ea71171b0a03b-01\n    [X-Amzn-Trace-Id] => Root=1-68b98f2a-e2d756db2366d612100e1cdd;Parent=1b0ea71171b0a03b;Sampled=1\n    [X-Adsk-Signature] => sha256=6462f044e00de765f72234d7722e0451adcebc87757e5fdc868f14fd45b6a62e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 63e698ff-de20-4b93-b32b-c15e959d9e89\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 13:07:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"63e698ff-de20-4b93-b32b-c15e959d9e89","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1018933","transactionId":"b9acd212-0136-5bbb-8197-b30d876e92ec","quoteStatus":"Order Submitted","message":"Quote# Q-1018933 status changed to Order Submitted.","modifiedAt":"2025-09-04T13:07:54.097Z"},"publishedAt":"2025-09-04T13:07:54.000Z","csn":"5103159758"}
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 13:07:58
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:36]  Provided signature: sha256=7aca4cad712fdae9123c677473ab6067e4f879af7745e3389fdb22c3b1b41768
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:37]  Calculated signature: sha256=7aca4cad712fdae9123c677473ab6067e4f879af7745e3389fdb22c3b1b41768
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 432207f4aec452ff\n    [X-B3-Traceid] => 68b98f2cedadcf2f5b2c2ca09c33ef5b\n    [B3] => 68b98f2cedadcf2f5b2c2ca09c33ef5b-432207f4aec452ff-1\n    [Traceparent] => 00-68b98f2cedadcf2f5b2c2ca09c33ef5b-432207f4aec452ff-01\n    [X-Amzn-Trace-Id] => Root=1-68b98f2c-edadcf2f5b2c2ca09c33ef5b;Parent=432207f4aec452ff;Sampled=1\n    [X-Adsk-Signature] => sha256=7aca4cad712fdae9123c677473ab6067e4f879af7745e3389fdb22c3b1b41768\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a0987115-3684-4d86-b0c5-402811622a74\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"a0987115-3684-4d86-b0c5-402811622a74","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1018933","transactionId":"b9acd212-0136-5bbb-8197-b30d876e92ec","quoteStatus":"Ordered","message":"Quote# Q-1018933 status changed to Ordered.","modifiedAt":"2025-09-04T13:07:55.849Z"},"publishedAt":"2025-09-04T13:07:56.000Z","csn":"5103159758"}
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 13:07:58
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:36]  Provided signature: sha256=cbc3219b9db7dd94ca9fff7cfaeb9366959238573a2cf5d39414a0b9f1f020c0
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:37]  Calculated signature: sha256=7aca4cad712fdae9123c677473ab6067e4f879af7745e3389fdb22c3b1b41768
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a9456d0fc3a1183f\n    [X-B3-Traceid] => 68b98f2cedadcf2f5b2c2ca09c33ef5b\n    [B3] => 68b98f2cedadcf2f5b2c2ca09c33ef5b-a9456d0fc3a1183f-1\n    [Traceparent] => 00-68b98f2cedadcf2f5b2c2ca09c33ef5b-a9456d0fc3a1183f-01\n    [X-Amzn-Trace-Id] => Root=1-68b98f2c-edadcf2f5b2c2ca09c33ef5b;Parent=a9456d0fc3a1183f;Sampled=1\n    [X-Adsk-Signature] => sha256=cbc3219b9db7dd94ca9fff7cfaeb9366959238573a2cf5d39414a0b9f1f020c0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a0987115-3684-4d86-b0c5-402811622a74\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 13:07:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"a0987115-3684-4d86-b0c5-402811622a74","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1018933","transactionId":"b9acd212-0136-5bbb-8197-b30d876e92ec","quoteStatus":"Ordered","message":"Quote# Q-1018933 status changed to Ordered.","modifiedAt":"2025-09-04T13:07:55.849Z"},"publishedAt":"2025-09-04T13:07:56.000Z","csn":"5103159758"}
[webhook] [2025-09-04 13:09:15] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 13:09:15
[webhook] [2025-09-04 13:09:15] [adwsapi_v2.php:36]  Provided signature: sha256=5985234bb2b6dfda19670120ca45d3c492d784429d548b6e2868c8de1ae0502d
[webhook] [2025-09-04 13:09:15] [adwsapi_v2.php:37]  Calculated signature: sha256=9a78f287a1f572a199c0610141d42cf1a9cf9d8575c2fef2aff87bfbb4c29852
[webhook] [2025-09-04 13:09:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 78202066bf589e72\n    [X-B3-Traceid] => 68b98f794d2dd47c6e3bdc5833af7259\n    [B3] => 68b98f794d2dd47c6e3bdc5833af7259-78202066bf589e72-1\n    [Traceparent] => 00-68b98f794d2dd47c6e3bdc5833af7259-78202066bf589e72-01\n    [X-Amzn-Trace-Id] => Root=1-68b98f79-4d2dd47c6e3bdc5833af7259;Parent=78202066bf589e72;Sampled=1\n    [X-Adsk-Signature] => sha256=5985234bb2b6dfda19670120ca45d3c492d784429d548b6e2868c8de1ae0502d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => da590026-9c7d-4db8-9abf-ef0b5306da84\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 13:09:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"da590026-9c7d-4db8-9abf-ef0b5306da84","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55388049199615","status":"Active","quantity":1,"endDate":"2026-09-06","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-09-04T12:39:11.000+0000"},"publishedAt":"2025-09-04T13:09:13.000Z","csn":"5103159758"}
[webhook] [2025-09-04 13:09:16] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 13:09:16
[webhook] [2025-09-04 13:09:16] [adwsapi_v2.php:36]  Provided signature: sha256=9a78f287a1f572a199c0610141d42cf1a9cf9d8575c2fef2aff87bfbb4c29852
[webhook] [2025-09-04 13:09:16] [adwsapi_v2.php:37]  Calculated signature: sha256=9a78f287a1f572a199c0610141d42cf1a9cf9d8575c2fef2aff87bfbb4c29852
[webhook] [2025-09-04 13:09:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 94343f2eb43e7755\n    [X-B3-Traceid] => 68b98f794d2dd47c6e3bdc5833af7259\n    [B3] => 68b98f794d2dd47c6e3bdc5833af7259-94343f2eb43e7755-1\n    [Traceparent] => 00-68b98f794d2dd47c6e3bdc5833af7259-94343f2eb43e7755-01\n    [X-Amzn-Trace-Id] => Root=1-68b98f79-4d2dd47c6e3bdc5833af7259;Parent=94343f2eb43e7755;Sampled=1\n    [X-Adsk-Signature] => sha256=9a78f287a1f572a199c0610141d42cf1a9cf9d8575c2fef2aff87bfbb4c29852\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => da590026-9c7d-4db8-9abf-ef0b5306da84\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 13:09:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"da590026-9c7d-4db8-9abf-ef0b5306da84","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55388049199615","status":"Active","quantity":1,"endDate":"2026-09-06","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-09-04T12:39:11.000+0000"},"publishedAt":"2025-09-04T13:09:13.000Z","csn":"5103159758"}
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 13:38:07
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:36]  Provided signature: sha256=e23a05f9a4a7c6942e3118143afbcf3dab019407631439aa33796e020c9103a2
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:37]  Calculated signature: sha256=f02e5b53aacad5feeb085f81bbcae0ea307ab3ed8b7917573f73c095ab9c4d98
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 533b4e4f53665c3e\n    [X-B3-Traceid] => 68b9963c561617983ddb736b6c650bfb\n    [B3] => 68b9963c561617983ddb736b6c650bfb-533b4e4f53665c3e-1\n    [Traceparent] => 00-68b9963c561617983ddb736b6c650bfb-533b4e4f53665c3e-01\n    [X-Amzn-Trace-Id] => Root=1-68b9963c-561617983ddb736b6c650bfb;Parent=533b4e4f53665c3e;Sampled=1\n    [X-Adsk-Signature] => sha256=e23a05f9a4a7c6942e3118143afbcf3dab019407631439aa33796e020c9103a2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2b1f022e-1915-4df5-8e39-9f7f48677624\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"2b1f022e-1915-4df5-8e39-9f7f48677624","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56891518358922","status":"Active","quantity":1,"endDate":"2028-09-15","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-09-04T13:08:02.000+0000"},"publishedAt":"2025-09-04T13:38:04.000Z","csn":"5103159758"}
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 13:38:07
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:36]  Provided signature: sha256=f02e5b53aacad5feeb085f81bbcae0ea307ab3ed8b7917573f73c095ab9c4d98
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:37]  Calculated signature: sha256=f02e5b53aacad5feeb085f81bbcae0ea307ab3ed8b7917573f73c095ab9c4d98
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7232c5de2bdf98e2\n    [X-B3-Traceid] => 68b9963c561617983ddb736b6c650bfb\n    [B3] => 68b9963c561617983ddb736b6c650bfb-7232c5de2bdf98e2-1\n    [Traceparent] => 00-68b9963c561617983ddb736b6c650bfb-7232c5de2bdf98e2-01\n    [X-Amzn-Trace-Id] => Root=1-68b9963c-561617983ddb736b6c650bfb;Parent=7232c5de2bdf98e2;Sampled=1\n    [X-Adsk-Signature] => sha256=f02e5b53aacad5feeb085f81bbcae0ea307ab3ed8b7917573f73c095ab9c4d98\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2b1f022e-1915-4df5-8e39-9f7f48677624\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 13:38:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"2b1f022e-1915-4df5-8e39-9f7f48677624","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56891518358922","status":"Active","quantity":1,"endDate":"2028-09-15","autoRenew":"ON","term":"3 Year","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-09-04T13:08:02.000+0000"},"publishedAt":"2025-09-04T13:38:04.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:00:48
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:36]  Provided signature: sha256=7b37162c69df83e0999048ab374d2dfa8cfc85650b68f6d50754369fedd8ffec
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:37]  Calculated signature: sha256=fc3b05c69c3a2535632a60dea184c76f9b608e2fd88ed0e276c2aaf9cb5ef50c
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 852ce39a7dcf3def\n    [X-B3-Traceid] => 68b99b8e8380c11e6061055098f12414\n    [B3] => 68b99b8e8380c11e6061055098f12414-852ce39a7dcf3def-1\n    [Traceparent] => 00-68b99b8e8380c11e6061055098f12414-852ce39a7dcf3def-01\n    [X-Amzn-Trace-Id] => Root=1-68b99b8e-8380c11e6061055098f12414;Parent=852ce39a7dcf3def;Sampled=1\n    [X-Adsk-Signature] => sha256=7b37162c69df83e0999048ab374d2dfa8cfc85650b68f6d50754369fedd8ffec\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ab941a7e-5f9b-451a-9324-ca40aef3d315\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"ab941a7e-5f9b-451a-9324-ca40aef3d315","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061147","transactionId":"a9c0b587-94f8-5432-bb86-d4b409451498","quoteStatus":"Draft","message":"Quote# Q-1061147 status changed to Draft.","modifiedAt":"2025-09-04T14:00:46.137Z"},"publishedAt":"2025-09-04T14:00:46.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:00:48
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:36]  Provided signature: sha256=fc3b05c69c3a2535632a60dea184c76f9b608e2fd88ed0e276c2aaf9cb5ef50c
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:37]  Calculated signature: sha256=fc3b05c69c3a2535632a60dea184c76f9b608e2fd88ed0e276c2aaf9cb5ef50c
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 32994fef332915df\n    [X-B3-Traceid] => 68b99b8e8380c11e6061055098f12414\n    [B3] => 68b99b8e8380c11e6061055098f12414-32994fef332915df-1\n    [Traceparent] => 00-68b99b8e8380c11e6061055098f12414-32994fef332915df-01\n    [X-Amzn-Trace-Id] => Root=1-68b99b8e-8380c11e6061055098f12414;Parent=32994fef332915df;Sampled=1\n    [X-Adsk-Signature] => sha256=fc3b05c69c3a2535632a60dea184c76f9b608e2fd88ed0e276c2aaf9cb5ef50c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ab941a7e-5f9b-451a-9324-ca40aef3d315\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:00:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"ab941a7e-5f9b-451a-9324-ca40aef3d315","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061147","transactionId":"a9c0b587-94f8-5432-bb86-d4b409451498","quoteStatus":"Draft","message":"Quote# Q-1061147 status changed to Draft.","modifiedAt":"2025-09-04T14:00:46.137Z"},"publishedAt":"2025-09-04T14:00:46.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:02:06
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:36]  Provided signature: sha256=762f3db921fc862aab6b1803af66c581ce00c4f5108bfeba4261a2ab5ca40a58
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:37]  Calculated signature: sha256=762f3db921fc862aab6b1803af66c581ce00c4f5108bfeba4261a2ab5ca40a58
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 709efa69672c54e8\n    [X-B3-Traceid] => 68b99bdbaef627f17f9c8941e2c32633\n    [B3] => 68b99bdbaef627f17f9c8941e2c32633-709efa69672c54e8-1\n    [Traceparent] => 00-68b99bdbaef627f17f9c8941e2c32633-709efa69672c54e8-01\n    [X-Amzn-Trace-Id] => Root=1-68b99bdb-aef627f17f9c8941e2c32633;Parent=709efa69672c54e8;Sampled=1\n    [X-Adsk-Signature] => sha256=762f3db921fc862aab6b1803af66c581ce00c4f5108bfeba4261a2ab5ca40a58\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8f23ab3e-a75f-451a-9712-7a8045dccbbf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"8f23ab3e-a75f-451a-9712-7a8045dccbbf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061147","transactionId":"a9c0b587-94f8-5432-bb86-d4b409451498","quoteStatus":"Quoted","message":"Quote# Q-1061147 status changed to Quoted.","modifiedAt":"2025-09-04T14:02:03.525Z"},"publishedAt":"2025-09-04T14:02:04.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:02:06
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:36]  Provided signature: sha256=519b6e49b83322f6c2398f5f66110eacbafeae88879413adb99cc229c73278c3
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:37]  Calculated signature: sha256=762f3db921fc862aab6b1803af66c581ce00c4f5108bfeba4261a2ab5ca40a58
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5bd74cb0f7fec1eb\n    [X-B3-Traceid] => 68b99bdbaef627f17f9c8941e2c32633\n    [B3] => 68b99bdbaef627f17f9c8941e2c32633-5bd74cb0f7fec1eb-1\n    [Traceparent] => 00-68b99bdbaef627f17f9c8941e2c32633-5bd74cb0f7fec1eb-01\n    [X-Amzn-Trace-Id] => Root=1-68b99bdb-aef627f17f9c8941e2c32633;Parent=5bd74cb0f7fec1eb;Sampled=1\n    [X-Adsk-Signature] => sha256=519b6e49b83322f6c2398f5f66110eacbafeae88879413adb99cc229c73278c3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8f23ab3e-a75f-451a-9712-7a8045dccbbf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:02:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"8f23ab3e-a75f-451a-9712-7a8045dccbbf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061147","transactionId":"a9c0b587-94f8-5432-bb86-d4b409451498","quoteStatus":"Quoted","message":"Quote# Q-1061147 status changed to Quoted.","modifiedAt":"2025-09-04T14:02:03.525Z"},"publishedAt":"2025-09-04T14:02:04.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:02:34
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:36]  Provided signature: sha256=7e49601a06e6126878d0e70926af08d8de8771252345cdf8678b8c2016d589a4
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:37]  Calculated signature: sha256=7e49601a06e6126878d0e70926af08d8de8771252345cdf8678b8c2016d589a4
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6306d6709ea4f0ad\n    [X-B3-Traceid] => 68b99bf7d3357f7ee81cdec0f88f02f9\n    [B3] => 68b99bf7d3357f7ee81cdec0f88f02f9-6306d6709ea4f0ad-1\n    [Traceparent] => 00-68b99bf7d3357f7ee81cdec0f88f02f9-6306d6709ea4f0ad-01\n    [X-Amzn-Trace-Id] => Root=1-68b99bf7-d3357f7ee81cdec0f88f02f9;Parent=6306d6709ea4f0ad;Sampled=1\n    [X-Adsk-Signature] => sha256=7e49601a06e6126878d0e70926af08d8de8771252345cdf8678b8c2016d589a4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ed9f257b-bf21-49a5-98a0-6bb296bfbf39\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"ed9f257b-bf21-49a5-98a0-6bb296bfbf39","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061156","transactionId":"bb30285f-8fec-5cce-857c-496c11c3fbb8","quoteStatus":"Draft","message":"Quote# Q-1061156 status changed to Draft.","modifiedAt":"2025-09-04T14:02:31.041Z"},"publishedAt":"2025-09-04T14:02:31.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:02:34
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:36]  Provided signature: sha256=256474240551c51c222280a41a40ddb2027981707a45b50aacff4309547c7efd
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:37]  Calculated signature: sha256=7e49601a06e6126878d0e70926af08d8de8771252345cdf8678b8c2016d589a4
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0d945edf402f7c6c\n    [X-B3-Traceid] => 68b99bf7d3357f7ee81cdec0f88f02f9\n    [B3] => 68b99bf7d3357f7ee81cdec0f88f02f9-0d945edf402f7c6c-1\n    [Traceparent] => 00-68b99bf7d3357f7ee81cdec0f88f02f9-0d945edf402f7c6c-01\n    [X-Amzn-Trace-Id] => Root=1-68b99bf7-d3357f7ee81cdec0f88f02f9;Parent=0d945edf402f7c6c;Sampled=1\n    [X-Adsk-Signature] => sha256=256474240551c51c222280a41a40ddb2027981707a45b50aacff4309547c7efd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ed9f257b-bf21-49a5-98a0-6bb296bfbf39\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:02:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"ed9f257b-bf21-49a5-98a0-6bb296bfbf39","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061156","transactionId":"bb30285f-8fec-5cce-857c-496c11c3fbb8","quoteStatus":"Draft","message":"Quote# Q-1061156 status changed to Draft.","modifiedAt":"2025-09-04T14:02:31.041Z"},"publishedAt":"2025-09-04T14:02:31.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:03:19
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:36]  Provided signature: sha256=bf5bf927815908060248182ea55d163ef53a952f64b01ba7d19170290a723076
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:37]  Calculated signature: sha256=bf5bf927815908060248182ea55d163ef53a952f64b01ba7d19170290a723076
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6712a993c60d82a3\n    [X-B3-Traceid] => 68b99c24e5c6a495159fb70d0d410c7a\n    [B3] => 68b99c24e5c6a495159fb70d0d410c7a-6712a993c60d82a3-1\n    [Traceparent] => 00-68b99c24e5c6a495159fb70d0d410c7a-6712a993c60d82a3-01\n    [X-Amzn-Trace-Id] => Root=1-68b99c24-e5c6a495159fb70d0d410c7a;Parent=6712a993c60d82a3;Sampled=1\n    [X-Adsk-Signature] => sha256=bf5bf927815908060248182ea55d163ef53a952f64b01ba7d19170290a723076\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e7ae52e6-1e68-4be5-8067-3a56e3b65fba\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"e7ae52e6-1e68-4be5-8067-3a56e3b65fba","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061156","transactionId":"bb30285f-8fec-5cce-857c-496c11c3fbb8","quoteStatus":"Quoted","message":"Quote# Q-1061156 status changed to Quoted.","modifiedAt":"2025-09-04T14:03:16.418Z"},"publishedAt":"2025-09-04T14:03:16.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:03:19
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:36]  Provided signature: sha256=c8c7fc5ab26e853ef7a7876e63ef8f2b306dc64be8d8157ac97f1927b778b0ac
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:37]  Calculated signature: sha256=bf5bf927815908060248182ea55d163ef53a952f64b01ba7d19170290a723076
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5a2439bc81def8ab\n    [X-B3-Traceid] => 68b99c24e5c6a495159fb70d0d410c7a\n    [B3] => 68b99c24e5c6a495159fb70d0d410c7a-5a2439bc81def8ab-1\n    [Traceparent] => 00-68b99c24e5c6a495159fb70d0d410c7a-5a2439bc81def8ab-01\n    [X-Amzn-Trace-Id] => Root=1-68b99c24-e5c6a495159fb70d0d410c7a;Parent=5a2439bc81def8ab;Sampled=1\n    [X-Adsk-Signature] => sha256=c8c7fc5ab26e853ef7a7876e63ef8f2b306dc64be8d8157ac97f1927b778b0ac\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e7ae52e6-1e68-4be5-8067-3a56e3b65fba\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:03:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"e7ae52e6-1e68-4be5-8067-3a56e3b65fba","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061156","transactionId":"bb30285f-8fec-5cce-857c-496c11c3fbb8","quoteStatus":"Quoted","message":"Quote# Q-1061156 status changed to Quoted.","modifiedAt":"2025-09-04T14:03:16.418Z"},"publishedAt":"2025-09-04T14:03:16.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:04:33
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:36]  Provided signature: sha256=3cf5cfe087f218e6e1ba0ab5a018508c7d227faed8317e5e4a2e3e24946df471
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:37]  Calculated signature: sha256=3cf5cfe087f218e6e1ba0ab5a018508c7d227faed8317e5e4a2e3e24946df471
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7fcbaf1784921195\n    [X-B3-Traceid] => 68b99c6f333c088130736a43de734150\n    [B3] => 68b99c6f333c088130736a43de734150-7fcbaf1784921195-1\n    [Traceparent] => 00-68b99c6f333c088130736a43de734150-7fcbaf1784921195-01\n    [X-Amzn-Trace-Id] => Root=1-68b99c6f-333c088130736a43de734150;Parent=7fcbaf1784921195;Sampled=1\n    [X-Adsk-Signature] => sha256=3cf5cfe087f218e6e1ba0ab5a018508c7d227faed8317e5e4a2e3e24946df471\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c59c0b52-b90a-4cf4-971e-7f8bf3bb97d3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"c59c0b52-b90a-4cf4-971e-7f8bf3bb97d3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061163","transactionId":"bca8bd6c-c778-535b-a31f-dc5f59e7601c","quoteStatus":"Draft","message":"Quote# Q-1061163 status changed to Draft.","modifiedAt":"2025-09-04T14:04:30.987Z"},"publishedAt":"2025-09-04T14:04:31.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:04:33
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:36]  Provided signature: sha256=a4187f5bf329ae792ec13b2477285df0bcf17c223e5441c9b79de729b930fd06
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:37]  Calculated signature: sha256=3cf5cfe087f218e6e1ba0ab5a018508c7d227faed8317e5e4a2e3e24946df471
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6e8fcbddfa173f11\n    [X-B3-Traceid] => 68b99c6f333c088130736a43de734150\n    [B3] => 68b99c6f333c088130736a43de734150-6e8fcbddfa173f11-1\n    [Traceparent] => 00-68b99c6f333c088130736a43de734150-6e8fcbddfa173f11-01\n    [X-Amzn-Trace-Id] => Root=1-68b99c6f-333c088130736a43de734150;Parent=6e8fcbddfa173f11;Sampled=1\n    [X-Adsk-Signature] => sha256=a4187f5bf329ae792ec13b2477285df0bcf17c223e5441c9b79de729b930fd06\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => c59c0b52-b90a-4cf4-971e-7f8bf3bb97d3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:04:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"c59c0b52-b90a-4cf4-971e-7f8bf3bb97d3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061163","transactionId":"bca8bd6c-c778-535b-a31f-dc5f59e7601c","quoteStatus":"Draft","message":"Quote# Q-1061163 status changed to Draft.","modifiedAt":"2025-09-04T14:04:30.987Z"},"publishedAt":"2025-09-04T14:04:31.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:05:25
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:36]  Provided signature: sha256=d91c6211992aacd8672239aec0b00bb034b2e6e21b06601b76710912b472b082
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:37]  Calculated signature: sha256=44b33cecee9677536e68e94b8a18738de433532deb4f6c7bc1eacd898f3f93f5
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fa9f95165d3594f5\n    [X-B3-Traceid] => 68b99ca2def9452bf28513242444d581\n    [B3] => 68b99ca2def9452bf28513242444d581-fa9f95165d3594f5-1\n    [Traceparent] => 00-68b99ca2def9452bf28513242444d581-fa9f95165d3594f5-01\n    [X-Amzn-Trace-Id] => Root=1-68b99ca2-def9452bf28513242444d581;Parent=fa9f95165d3594f5;Sampled=1\n    [X-Adsk-Signature] => sha256=d91c6211992aacd8672239aec0b00bb034b2e6e21b06601b76710912b472b082\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 31c80ade-aced-4245-a609-f4aa111faa95\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"31c80ade-aced-4245-a609-f4aa111faa95","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061163","transactionId":"bca8bd6c-c778-535b-a31f-dc5f59e7601c","quoteStatus":"Quoted","message":"Quote# Q-1061163 status changed to Quoted.","modifiedAt":"2025-09-04T14:05:22.504Z"},"publishedAt":"2025-09-04T14:05:22.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:05:25
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:36]  Provided signature: sha256=44b33cecee9677536e68e94b8a18738de433532deb4f6c7bc1eacd898f3f93f5
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:37]  Calculated signature: sha256=44b33cecee9677536e68e94b8a18738de433532deb4f6c7bc1eacd898f3f93f5
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2698e442c92e621d\n    [X-B3-Traceid] => 68b99ca2def9452bf28513242444d581\n    [B3] => 68b99ca2def9452bf28513242444d581-2698e442c92e621d-1\n    [Traceparent] => 00-68b99ca2def9452bf28513242444d581-2698e442c92e621d-01\n    [X-Amzn-Trace-Id] => Root=1-68b99ca2-def9452bf28513242444d581;Parent=2698e442c92e621d;Sampled=1\n    [X-Adsk-Signature] => sha256=44b33cecee9677536e68e94b8a18738de433532deb4f6c7bc1eacd898f3f93f5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 31c80ade-aced-4245-a609-f4aa111faa95\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:05:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"31c80ade-aced-4245-a609-f4aa111faa95","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1061163","transactionId":"bca8bd6c-c778-535b-a31f-dc5f59e7601c","quoteStatus":"Quoted","message":"Quote# Q-1061163 status changed to Quoted.","modifiedAt":"2025-09-04T14:05:22.504Z"},"publishedAt":"2025-09-04T14:05:22.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:40:17
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:36]  Provided signature: sha256=9975c1e7fce9d53618a18371d890cb4ad23a97034209932160d1391d6817d24c
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:37]  Calculated signature: sha256=9975c1e7fce9d53618a18371d890cb4ad23a97034209932160d1391d6817d24c
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3f3356bc046490d7\n    [X-B3-Traceid] => 68b9a4ce2231bd4e084dace125c92115\n    [B3] => 68b9a4ce2231bd4e084dace125c92115-3f3356bc046490d7-1\n    [Traceparent] => 00-68b9a4ce2231bd4e084dace125c92115-3f3356bc046490d7-01\n    [X-Amzn-Trace-Id] => Root=1-68b9a4ce-2231bd4e084dace125c92115;Parent=3f3356bc046490d7;Sampled=1\n    [X-Adsk-Signature] => sha256=9975c1e7fce9d53618a18371d890cb4ad23a97034209932160d1391d6817d24c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a1e7a000-7124-48df-b0ad-cb8e387bbca2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"a1e7a000-7124-48df-b0ad-cb8e387bbca2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73349919080022","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-09-04T14:20:12.000+0000"},"publishedAt":"2025-09-04T14:40:14.000Z","csn":"5103159758"}
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 14:40:17
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:36]  Provided signature: sha256=f41c8566a10bfec838f36638ce4db37c5eebe2d0a0bf3b60bf326322e1bbcf5b
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:37]  Calculated signature: sha256=9975c1e7fce9d53618a18371d890cb4ad23a97034209932160d1391d6817d24c
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fc5da9318f9054c3\n    [X-B3-Traceid] => 68b9a4ce2231bd4e084dace125c92115\n    [B3] => 68b9a4ce2231bd4e084dace125c92115-fc5da9318f9054c3-1\n    [Traceparent] => 00-68b9a4ce2231bd4e084dace125c92115-fc5da9318f9054c3-01\n    [X-Amzn-Trace-Id] => Root=1-68b9a4ce-2231bd4e084dace125c92115;Parent=fc5da9318f9054c3;Sampled=1\n    [X-Adsk-Signature] => sha256=f41c8566a10bfec838f36638ce4db37c5eebe2d0a0bf3b60bf326322e1bbcf5b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a1e7a000-7124-48df-b0ad-cb8e387bbca2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 14:40:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"a1e7a000-7124-48df-b0ad-cb8e387bbca2","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73349919080022","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-09-04T14:20:12.000+0000"},"publishedAt":"2025-09-04T14:40:14.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:21:38
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:36]  Provided signature: sha256=b0bb41ed5d130b6bfe14634bfbdeb7be0c6053f1e851fb5ae8136078c5aa8482
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:37]  Calculated signature: sha256=b0bb41ed5d130b6bfe14634bfbdeb7be0c6053f1e851fb5ae8136078c5aa8482
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 299490768ae38e71\n    [X-B3-Traceid] => 68b9ae7fd69780ef1e42cd01714d9876\n    [B3] => 68b9ae7fd69780ef1e42cd01714d9876-299490768ae38e71-1\n    [Traceparent] => 00-68b9ae7fd69780ef1e42cd01714d9876-299490768ae38e71-01\n    [X-Amzn-Trace-Id] => Root=1-68b9ae7f-d69780ef1e42cd01714d9876;Parent=299490768ae38e71;Sampled=1\n    [X-Adsk-Signature] => sha256=b0bb41ed5d130b6bfe14634bfbdeb7be0c6053f1e851fb5ae8136078c5aa8482\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 031535c3-1a3f-4f66-b85f-f7ffd6d74c7e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"031535c3-1a3f-4f66-b85f-f7ffd6d74c7e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060637","transactionId":"d1ec2463-5045-5998-8869-4550d7643639","quoteStatus":"Order Submitted","message":"Quote# Q-1060637 status changed to Order Submitted.","modifiedAt":"2025-09-04T15:21:35.116Z"},"publishedAt":"2025-09-04T15:21:35.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:21:38
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:36]  Provided signature: sha256=5dafe0587e1395604378fdcd231fa104b77aff875637de619cb48ce055790f22
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:37]  Calculated signature: sha256=b0bb41ed5d130b6bfe14634bfbdeb7be0c6053f1e851fb5ae8136078c5aa8482
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 60608be787f7a5b6\n    [X-B3-Traceid] => 68b9ae7fd69780ef1e42cd01714d9876\n    [B3] => 68b9ae7fd69780ef1e42cd01714d9876-60608be787f7a5b6-1\n    [Traceparent] => 00-68b9ae7fd69780ef1e42cd01714d9876-60608be787f7a5b6-01\n    [X-Amzn-Trace-Id] => Root=1-68b9ae7f-d69780ef1e42cd01714d9876;Parent=60608be787f7a5b6;Sampled=1\n    [X-Adsk-Signature] => sha256=5dafe0587e1395604378fdcd231fa104b77aff875637de619cb48ce055790f22\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 031535c3-1a3f-4f66-b85f-f7ffd6d74c7e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:21:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"031535c3-1a3f-4f66-b85f-f7ffd6d74c7e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060637","transactionId":"d1ec2463-5045-5998-8869-4550d7643639","quoteStatus":"Order Submitted","message":"Quote# Q-1060637 status changed to Order Submitted.","modifiedAt":"2025-09-04T15:21:35.116Z"},"publishedAt":"2025-09-04T15:21:35.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:21:40
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:36]  Provided signature: sha256=2b8850ce58da568f25eff5bb382baf3a51fb94ccca4b11216665049ce85b1eae
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:37]  Calculated signature: sha256=2b8850ce58da568f25eff5bb382baf3a51fb94ccca4b11216665049ce85b1eae
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a4c9a15faaf4669f\n    [X-B3-Traceid] => 68b9ae822f40cc55a6b414cbcf599ac4\n    [B3] => 68b9ae822f40cc55a6b414cbcf599ac4-a4c9a15faaf4669f-1\n    [Traceparent] => 00-68b9ae822f40cc55a6b414cbcf599ac4-a4c9a15faaf4669f-01\n    [X-Amzn-Trace-Id] => Root=1-68b9ae82-2f40cc55a6b414cbcf599ac4;Parent=a4c9a15faaf4669f;Sampled=1\n    [X-Adsk-Signature] => sha256=2b8850ce58da568f25eff5bb382baf3a51fb94ccca4b11216665049ce85b1eae\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 76fbafa7-32d5-4765-accc-142c8b2de162\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"76fbafa7-32d5-4765-accc-142c8b2de162","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060637","transactionId":"d1ec2463-5045-5998-8869-4550d7643639","quoteStatus":"Ordered","message":"Quote# Q-1060637 status changed to Ordered.","modifiedAt":"2025-09-04T15:21:38.054Z"},"publishedAt":"2025-09-04T15:21:38.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:21:40
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:36]  Provided signature: sha256=7afc4b0e5756d1338972b74d1a7a32de09e69ba4d97f3e5a40a39a27c187e3dc
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:37]  Calculated signature: sha256=2b8850ce58da568f25eff5bb382baf3a51fb94ccca4b11216665049ce85b1eae
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 310e1058691b213b\n    [X-B3-Traceid] => 68b9ae822f40cc55a6b414cbcf599ac4\n    [B3] => 68b9ae822f40cc55a6b414cbcf599ac4-310e1058691b213b-1\n    [Traceparent] => 00-68b9ae822f40cc55a6b414cbcf599ac4-310e1058691b213b-01\n    [X-Amzn-Trace-Id] => Root=1-68b9ae82-2f40cc55a6b414cbcf599ac4;Parent=310e1058691b213b;Sampled=1\n    [X-Adsk-Signature] => sha256=7afc4b0e5756d1338972b74d1a7a32de09e69ba4d97f3e5a40a39a27c187e3dc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 76fbafa7-32d5-4765-accc-142c8b2de162\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:21:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"76fbafa7-32d5-4765-accc-142c8b2de162","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1060637","transactionId":"d1ec2463-5045-5998-8869-4550d7643639","quoteStatus":"Ordered","message":"Quote# Q-1060637 status changed to Ordered.","modifiedAt":"2025-09-04T15:21:38.054Z"},"publishedAt":"2025-09-04T15:21:38.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:39:52
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:36]  Provided signature: sha256=867426c978e7e46d01d8e1a56c1705c65b719575a184b64e196280a09f0c9807
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:37]  Calculated signature: sha256=867426c978e7e46d01d8e1a56c1705c65b719575a184b64e196280a09f0c9807
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 90b931897b61aeed\n    [X-B3-Traceid] => 68b9b2c660117cbe0193c4f45e8952b4\n    [B3] => 68b9b2c660117cbe0193c4f45e8952b4-90b931897b61aeed-1\n    [Traceparent] => 00-68b9b2c660117cbe0193c4f45e8952b4-90b931897b61aeed-01\n    [X-Amzn-Trace-Id] => Root=1-68b9b2c6-60117cbe0193c4f45e8952b4;Parent=90b931897b61aeed;Sampled=1\n    [X-Adsk-Signature] => sha256=867426c978e7e46d01d8e1a56c1705c65b719575a184b64e196280a09f0c9807\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5c0e33ec-c310-415e-b2e6-8cea55b4ffdc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"5c0e33ec-c310-415e-b2e6-8cea55b4ffdc","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55388049199615","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T15:09:44.000+0000"},"publishedAt":"2025-09-04T15:39:50.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:39:52
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:36]  Provided signature: sha256=3372258450344a8b88fce010af4add8b593dc718abd548858195b1a7f0172917
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:37]  Calculated signature: sha256=867426c978e7e46d01d8e1a56c1705c65b719575a184b64e196280a09f0c9807
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fa45d49b3f8f4eb8\n    [X-B3-Traceid] => 68b9b2c660117cbe0193c4f45e8952b4\n    [B3] => 68b9b2c660117cbe0193c4f45e8952b4-fa45d49b3f8f4eb8-1\n    [Traceparent] => 00-68b9b2c660117cbe0193c4f45e8952b4-fa45d49b3f8f4eb8-01\n    [X-Amzn-Trace-Id] => Root=1-68b9b2c6-60117cbe0193c4f45e8952b4;Parent=fa45d49b3f8f4eb8;Sampled=1\n    [X-Adsk-Signature] => sha256=3372258450344a8b88fce010af4add8b593dc718abd548858195b1a7f0172917\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5c0e33ec-c310-415e-b2e6-8cea55b4ffdc\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:39:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"5c0e33ec-c310-415e-b2e6-8cea55b4ffdc","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55388049199615","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T15:09:44.000+0000"},"publishedAt":"2025-09-04T15:39:50.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:40:16
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:36]  Provided signature: sha256=870768a2189f2f33a2a4021eca00442e78851814d09c395a50d5a9a7ad213f81
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:37]  Calculated signature: sha256=870768a2189f2f33a2a4021eca00442e78851814d09c395a50d5a9a7ad213f81
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ea50e8f6af94467c\n    [X-B3-Traceid] => 68b9b2de35378dde7e3873a33fe2ff3b\n    [B3] => 68b9b2de35378dde7e3873a33fe2ff3b-ea50e8f6af94467c-1\n    [Traceparent] => 00-68b9b2de35378dde7e3873a33fe2ff3b-ea50e8f6af94467c-01\n    [X-Amzn-Trace-Id] => Root=1-68b9b2de-35378dde7e3873a33fe2ff3b;Parent=ea50e8f6af94467c;Sampled=1\n    [X-Adsk-Signature] => sha256=870768a2189f2f33a2a4021eca00442e78851814d09c395a50d5a9a7ad213f81\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2a6a1d88-a0a9-403a-ac31-1a9f69a356f3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"2a6a1d88-a0a9-403a-ac31-1a9f69a356f3","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56891518358922","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T15:10:07.000+0000"},"publishedAt":"2025-09-04T15:40:14.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:40:16
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:36]  Provided signature: sha256=07df104e7fe1c943eebd6816688f7bf590a6e9022cd8d55036f13536d734a534
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:37]  Calculated signature: sha256=870768a2189f2f33a2a4021eca00442e78851814d09c395a50d5a9a7ad213f81
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4b3697342f02eab6\n    [X-B3-Traceid] => 68b9b2de35378dde7e3873a33fe2ff3b\n    [B3] => 68b9b2de35378dde7e3873a33fe2ff3b-4b3697342f02eab6-1\n    [Traceparent] => 00-68b9b2de35378dde7e3873a33fe2ff3b-4b3697342f02eab6-01\n    [X-Amzn-Trace-Id] => Root=1-68b9b2de-35378dde7e3873a33fe2ff3b;Parent=4b3697342f02eab6;Sampled=1\n    [X-Adsk-Signature] => sha256=07df104e7fe1c943eebd6816688f7bf590a6e9022cd8d55036f13536d734a534\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2a6a1d88-a0a9-403a-ac31-1a9f69a356f3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:40:16] [adwsapi_v2.php:57]  Received webhook data: {"id":"2a6a1d88-a0a9-403a-ac31-1a9f69a356f3","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56891518358922","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T15:10:07.000+0000"},"publishedAt":"2025-09-04T15:40:14.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:41:52
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:36]  Provided signature: sha256=66190149afe6c51c37d4310969034babd61e065916891ea984abbccb2f6717c2
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:37]  Calculated signature: sha256=5d082b36d5dcc1c73a798bc8cc1198002071d320e7bc9d6daac432d3938ad067
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cf24aad39206fd11\n    [X-B3-Traceid] => 68b9b33e1027c83f1751667741a2e688\n    [B3] => 68b9b33e1027c83f1751667741a2e688-cf24aad39206fd11-1\n    [Traceparent] => 00-68b9b33e1027c83f1751667741a2e688-cf24aad39206fd11-01\n    [X-Amzn-Trace-Id] => Root=1-68b9b33e-1027c83f1751667741a2e688;Parent=cf24aad39206fd11;Sampled=1\n    [X-Adsk-Signature] => sha256=66190149afe6c51c37d4310969034babd61e065916891ea984abbccb2f6717c2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 34ac59f9-93fc-4a8e-b5a5-3931bdba580e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"34ac59f9-93fc-4a8e-b5a5-3931bdba580e","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55536230799844","status":"Active","quantity":1,"endDate":"2026-09-16","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-09-04T15:21:43.000+0000"},"publishedAt":"2025-09-04T15:41:50.000Z","csn":"5103159758"}
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 15:41:52
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:36]  Provided signature: sha256=5d082b36d5dcc1c73a798bc8cc1198002071d320e7bc9d6daac432d3938ad067
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:37]  Calculated signature: sha256=5d082b36d5dcc1c73a798bc8cc1198002071d320e7bc9d6daac432d3938ad067
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 04b4706accdef0e7\n    [X-B3-Traceid] => 68b9b33e1027c83f1751667741a2e688\n    [B3] => 68b9b33e1027c83f1751667741a2e688-04b4706accdef0e7-1\n    [Traceparent] => 00-68b9b33e1027c83f1751667741a2e688-04b4706accdef0e7-01\n    [X-Amzn-Trace-Id] => Root=1-68b9b33e-1027c83f1751667741a2e688;Parent=04b4706accdef0e7;Sampled=1\n    [X-Adsk-Signature] => sha256=5d082b36d5dcc1c73a798bc8cc1198002071d320e7bc9d6daac432d3938ad067\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 34ac59f9-93fc-4a8e-b5a5-3931bdba580e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 15:41:52] [adwsapi_v2.php:57]  Received webhook data: {"id":"34ac59f9-93fc-4a8e-b5a5-3931bdba580e","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55536230799844","status":"Active","quantity":1,"endDate":"2026-09-16","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-09-04T15:21:43.000+0000"},"publishedAt":"2025-09-04T15:41:50.000Z","csn":"5103159758"}
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 16:41:09
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:36]  Provided signature: sha256=4fe3f92e3a58afdce43e68d9918ddd5f1e5d11d4150bf0498c122e301bd621a3
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:37]  Calculated signature: sha256=4fe3f92e3a58afdce43e68d9918ddd5f1e5d11d4150bf0498c122e301bd621a3
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f3b0b219f09c454b\n    [X-B3-Traceid] => 68b9c123795e322c69122ab10c14179b\n    [B3] => 68b9c123795e322c69122ab10c14179b-f3b0b219f09c454b-1\n    [Traceparent] => 00-68b9c123795e322c69122ab10c14179b-f3b0b219f09c454b-01\n    [X-Amzn-Trace-Id] => Root=1-68b9c123-795e322c69122ab10c14179b;Parent=f3b0b219f09c454b;Sampled=1\n    [X-Adsk-Signature] => sha256=4fe3f92e3a58afdce43e68d9918ddd5f1e5d11d4150bf0498c122e301bd621a3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757004067447-55388049199615-9033828758-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757004067447-55388049199615-9033828758-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55388049199615","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T16:41:07.447Z"},"publishedAt":"2025-09-04T16:41:07.000Z","csn":"5103159758"}
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 16:41:09
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:36]  Provided signature: sha256=7733928bc0079c0778cba1f0be95ae312279efd5c8335bdacc094b066af8c2aa
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:37]  Calculated signature: sha256=4fe3f92e3a58afdce43e68d9918ddd5f1e5d11d4150bf0498c122e301bd621a3
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bc1d53b6dacb3c7b\n    [X-B3-Traceid] => 68b9c123795e322c69122ab10c14179b\n    [B3] => 68b9c123795e322c69122ab10c14179b-bc1d53b6dacb3c7b-1\n    [Traceparent] => 00-68b9c123795e322c69122ab10c14179b-bc1d53b6dacb3c7b-01\n    [X-Amzn-Trace-Id] => Root=1-68b9c123-795e322c69122ab10c14179b;Parent=bc1d53b6dacb3c7b;Sampled=1\n    [X-Adsk-Signature] => sha256=7733928bc0079c0778cba1f0be95ae312279efd5c8335bdacc094b066af8c2aa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757004067447-55388049199615-9033828758-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 16:41:09] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757004067447-55388049199615-9033828758-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55388049199615","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T16:41:07.447Z"},"publishedAt":"2025-09-04T16:41:07.000Z","csn":"5103159758"}
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 19:35:58
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:36]  Provided signature: sha256=0723b712980350d28d233e1f094ef574a70433bdb6fd9d5560e7b7805bc4eafa
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:37]  Calculated signature: sha256=73a9a3387b429c61b8c8a3e699d4f968a7081af99fd9a5cd50aea49dd6927ce9
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d37c015709cd69d4\n    [X-B3-Traceid] => 68b9ea1b23c47c253a5aef3d77489f5e\n    [B3] => 68b9ea1b23c47c253a5aef3d77489f5e-d37c015709cd69d4-1\n    [Traceparent] => 00-68b9ea1b23c47c253a5aef3d77489f5e-d37c015709cd69d4-01\n    [X-Amzn-Trace-Id] => Root=1-68b9ea1b-23c47c253a5aef3d77489f5e;Parent=d37c015709cd69d4;Sampled=1\n    [X-Adsk-Signature] => sha256=0723b712980350d28d233e1f094ef574a70433bdb6fd9d5560e7b7805bc4eafa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 572434d4-9525-4b89-af7e-a96c23d86e9f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"572434d4-9525-4b89-af7e-a96c23d86e9f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55536230799844","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T19:10:46.000+0000"},"publishedAt":"2025-09-04T19:35:55.000Z","csn":"5103159758"}
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 19:35:58
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:36]  Provided signature: sha256=73a9a3387b429c61b8c8a3e699d4f968a7081af99fd9a5cd50aea49dd6927ce9
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:37]  Calculated signature: sha256=73a9a3387b429c61b8c8a3e699d4f968a7081af99fd9a5cd50aea49dd6927ce9
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d99aaa008781476e\n    [X-B3-Traceid] => 68b9ea1b23c47c253a5aef3d77489f5e\n    [B3] => 68b9ea1b23c47c253a5aef3d77489f5e-d99aaa008781476e-1\n    [Traceparent] => 00-68b9ea1b23c47c253a5aef3d77489f5e-d99aaa008781476e-01\n    [X-Amzn-Trace-Id] => Root=1-68b9ea1b-23c47c253a5aef3d77489f5e;Parent=d99aaa008781476e;Sampled=1\n    [X-Adsk-Signature] => sha256=73a9a3387b429c61b8c8a3e699d4f968a7081af99fd9a5cd50aea49dd6927ce9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 572434d4-9525-4b89-af7e-a96c23d86e9f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 19:35:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"572434d4-9525-4b89-af7e-a96c23d86e9f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55536230799844","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-09-04T19:10:46.000+0000"},"publishedAt":"2025-09-04T19:35:55.000Z","csn":"5103159758"}
[webhook] [2025-09-04 20:40:15] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 20:40:15
[webhook] [2025-09-04 20:40:15] [adwsapi_v2.php:36]  Provided signature: sha256=0eb0dbcb4ef9dde57ed8142a7ee754d6050cec050623b4a1095f7409db7a4823
[webhook] [2025-09-04 20:40:15] [adwsapi_v2.php:37]  Calculated signature: sha256=0eb0dbcb4ef9dde57ed8142a7ee754d6050cec050623b4a1095f7409db7a4823
[webhook] [2025-09-04 20:40:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f11ee0a3e1c3c5c6\n    [X-B3-Traceid] => 68b9f92d2f4fe69861417d39301a890f\n    [B3] => 68b9f92d2f4fe69861417d39301a890f-f11ee0a3e1c3c5c6-1\n    [Traceparent] => 00-68b9f92d2f4fe69861417d39301a890f-f11ee0a3e1c3c5c6-01\n    [X-Amzn-Trace-Id] => Root=1-68b9f92d-2f4fe69861417d39301a890f;Parent=f11ee0a3e1c3c5c6;Sampled=1\n    [X-Adsk-Signature] => sha256=0eb0dbcb4ef9dde57ed8142a7ee754d6050cec050623b4a1095f7409db7a4823\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757018413397-55388049199615-9033828758-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 20:40:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757018413397-55388049199615-9033828758-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55388049199615","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T20:40:13.397Z"},"publishedAt":"2025-09-04T20:40:13.000Z","csn":"5103159758"}
[webhook] [2025-09-04 20:40:20] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 20:40:20
[webhook] [2025-09-04 20:40:20] [adwsapi_v2.php:36]  Provided signature: sha256=e4c658f98fa0472af55b545be6afd99f76ac1db0477bc6249d9dddb696c24b0b
[webhook] [2025-09-04 20:40:20] [adwsapi_v2.php:37]  Calculated signature: sha256=0eb0dbcb4ef9dde57ed8142a7ee754d6050cec050623b4a1095f7409db7a4823
[webhook] [2025-09-04 20:40:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a64a0c64b6f11ab1\n    [X-B3-Traceid] => 68b9f92d2f4fe69861417d39301a890f\n    [B3] => 68b9f92d2f4fe69861417d39301a890f-a64a0c64b6f11ab1-1\n    [Traceparent] => 00-68b9f92d2f4fe69861417d39301a890f-a64a0c64b6f11ab1-01\n    [X-Amzn-Trace-Id] => Root=1-68b9f92d-2f4fe69861417d39301a890f;Parent=a64a0c64b6f11ab1;Sampled=1\n    [X-Adsk-Signature] => sha256=e4c658f98fa0472af55b545be6afd99f76ac1db0477bc6249d9dddb696c24b0b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757018413397-55388049199615-9033828758-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 20:40:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757018413397-55388049199615-9033828758-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55388049199615","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T20:40:13.397Z"},"publishedAt":"2025-09-04T20:40:13.000Z","csn":"5103159758"}
[webhook] [2025-09-04 20:51:34] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 20:51:34
[webhook] [2025-09-04 20:51:34] [adwsapi_v2.php:36]  Provided signature: sha256=78bede38e4eb2aaf8a648e4d873f91aa745d7c8e74d421fd32aa8c354743c369
[webhook] [2025-09-04 20:51:34] [adwsapi_v2.php:37]  Calculated signature: sha256=78bede38e4eb2aaf8a648e4d873f91aa745d7c8e74d421fd32aa8c354743c369
[webhook] [2025-09-04 20:51:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 69cec24d70419183\n    [X-B3-Traceid] => 68b9fbd37bc2bf3820d87d4a4f40eefc\n    [B3] => 68b9fbd37bc2bf3820d87d4a4f40eefc-69cec24d70419183-1\n    [Traceparent] => 00-68b9fbd37bc2bf3820d87d4a4f40eefc-69cec24d70419183-01\n    [X-Amzn-Trace-Id] => Root=1-68b9fbd3-7bc2bf3820d87d4a4f40eefc;Parent=69cec24d70419183;Sampled=1\n    [X-Adsk-Signature] => sha256=78bede38e4eb2aaf8a648e4d873f91aa745d7c8e74d421fd32aa8c354743c369\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757019091960-55536230799844-9033829259-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 20:51:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757019091960-55536230799844-9033829259-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55536230799844","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T20:51:31.960Z"},"publishedAt":"2025-09-04T20:51:32.000Z","csn":"5103159758"}
[webhook] [2025-09-04 20:51:42] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 20:51:42
[webhook] [2025-09-04 20:51:42] [adwsapi_v2.php:36]  Provided signature: sha256=76e151e5f5498b42377c6b145491ee1eff2ecec6e09f787fc56662c186c1ce70
[webhook] [2025-09-04 20:51:42] [adwsapi_v2.php:37]  Calculated signature: sha256=78bede38e4eb2aaf8a648e4d873f91aa745d7c8e74d421fd32aa8c354743c369
[webhook] [2025-09-04 20:51:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 976290e527b10b70\n    [X-B3-Traceid] => 68b9fbd37bc2bf3820d87d4a4f40eefc\n    [B3] => 68b9fbd37bc2bf3820d87d4a4f40eefc-976290e527b10b70-1\n    [Traceparent] => 00-68b9fbd37bc2bf3820d87d4a4f40eefc-976290e527b10b70-01\n    [X-Amzn-Trace-Id] => Root=1-68b9fbd3-7bc2bf3820d87d4a4f40eefc;Parent=976290e527b10b70;Sampled=1\n    [X-Adsk-Signature] => sha256=76e151e5f5498b42377c6b145491ee1eff2ecec6e09f787fc56662c186c1ce70\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757019091960-55536230799844-9033829259-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 20:51:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757019091960-55536230799844-9033829259-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55536230799844","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T20:51:31.960Z"},"publishedAt":"2025-09-04T20:51:32.000Z","csn":"5103159758"}
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 20:58:47
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:36]  Provided signature: sha256=48297c1f26a3a2ba5e6338dcd0e178fb2dcd8eab12d645a2b648971efd32a082
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:37]  Calculated signature: sha256=48297c1f26a3a2ba5e6338dcd0e178fb2dcd8eab12d645a2b648971efd32a082
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4d11b92094188b55\n    [X-B3-Traceid] => 68b9fd851e9e22893ec05b012cbf8c72\n    [B3] => 68b9fd851e9e22893ec05b012cbf8c72-4d11b92094188b55-1\n    [Traceparent] => 00-68b9fd851e9e22893ec05b012cbf8c72-4d11b92094188b55-01\n    [X-Amzn-Trace-Id] => Root=1-68b9fd85-1e9e22893ec05b012cbf8c72;Parent=4d11b92094188b55;Sampled=1\n    [X-Adsk-Signature] => sha256=48297c1f26a3a2ba5e6338dcd0e178fb2dcd8eab12d645a2b648971efd32a082\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757019525688-56891518358922-9033828906-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757019525688-56891518358922-9033828906-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56891518358922","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T20:58:45.688Z"},"publishedAt":"2025-09-04T20:58:45.000Z","csn":"5103159758"}
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 20:58:48
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:36]  Provided signature: sha256=a4b27a9e1c0c8d4bbd7a411e38e0d3e7d9aa47d72767f09e72da7273725c5db7
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:37]  Calculated signature: sha256=48297c1f26a3a2ba5e6338dcd0e178fb2dcd8eab12d645a2b648971efd32a082
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2a8a8b209dd167de\n    [X-B3-Traceid] => 68b9fd851e9e22893ec05b012cbf8c72\n    [B3] => 68b9fd851e9e22893ec05b012cbf8c72-2a8a8b209dd167de-1\n    [Traceparent] => 00-68b9fd851e9e22893ec05b012cbf8c72-2a8a8b209dd167de-01\n    [X-Amzn-Trace-Id] => Root=1-68b9fd85-1e9e22893ec05b012cbf8c72;Parent=2a8a8b209dd167de;Sampled=1\n    [X-Adsk-Signature] => sha256=a4b27a9e1c0c8d4bbd7a411e38e0d3e7d9aa47d72767f09e72da7273725c5db7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757019525688-56891518358922-9033828906-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 20:58:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757019525688-56891518358922-9033828906-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"56891518358922","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T20:58:45.688Z"},"publishedAt":"2025-09-04T20:58:45.000Z","csn":"5103159758"}
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 22:26:34
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:36]  Provided signature: sha256=272fa9519e280b66eb1a9a5ae3427208158d1f1152047615cae64d182cb0ecbd
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:37]  Calculated signature: sha256=776eb35247ba912e3489f8895dba50879adcbc3f460a571e789e7418389c4c62
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b5a6ac08cc2703ea\n    [X-B3-Traceid] => 68ba12183d72c1064e061bd523611b86\n    [B3] => 68ba12183d72c1064e061bd523611b86-b5a6ac08cc2703ea-1\n    [Traceparent] => 00-68ba12183d72c1064e061bd523611b86-b5a6ac08cc2703ea-01\n    [X-Amzn-Trace-Id] => Root=1-68ba1218-3d72c1064e061bd523611b86;Parent=b5a6ac08cc2703ea;Sampled=1\n    [X-Adsk-Signature] => sha256=272fa9519e280b66eb1a9a5ae3427208158d1f1152047615cae64d182cb0ecbd\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757024792019-55536230799844-9033829259-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757024792019-55536230799844-9033829259-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55536230799844","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T22:26:32.019Z"},"publishedAt":"2025-09-04T22:26:32.000Z","csn":"5103159758"}
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 22:26:34
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:36]  Provided signature: sha256=776eb35247ba912e3489f8895dba50879adcbc3f460a571e789e7418389c4c62
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:37]  Calculated signature: sha256=776eb35247ba912e3489f8895dba50879adcbc3f460a571e789e7418389c4c62
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6813750069b0afe0\n    [X-B3-Traceid] => 68ba12183d72c1064e061bd523611b86\n    [B3] => 68ba12183d72c1064e061bd523611b86-6813750069b0afe0-1\n    [Traceparent] => 00-68ba12183d72c1064e061bd523611b86-6813750069b0afe0-01\n    [X-Amzn-Trace-Id] => Root=1-68ba1218-3d72c1064e061bd523611b86;Parent=6813750069b0afe0;Sampled=1\n    [X-Adsk-Signature] => sha256=776eb35247ba912e3489f8895dba50879adcbc3f460a571e789e7418389c4c62\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757024792019-55536230799844-9033829259-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 22:26:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757024792019-55536230799844-9033829259-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55536230799844","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-04T22:26:32.019Z"},"publishedAt":"2025-09-04T22:26:32.000Z","csn":"5103159758"}
[webhook] [2025-09-04 23:01:36] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 23:01:36
[webhook] [2025-09-04 23:01:36] [adwsapi_v2.php:36]  Provided signature: sha256=6a1db2e29441acc429e5214b2f7462b6c23a953513f87870a322d9a91df95cfb
[webhook] [2025-09-04 23:01:36] [adwsapi_v2.php:37]  Calculated signature: sha256=6a1db2e29441acc429e5214b2f7462b6c23a953513f87870a322d9a91df95cfb
[webhook] [2025-09-04 23:01:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f00dceebebf33fc3\n    [X-B3-Traceid] => 68ba1a4d75efd76636b47002284e8152\n    [B3] => 68ba1a4d75efd76636b47002284e8152-f00dceebebf33fc3-1\n    [Traceparent] => 00-68ba1a4d75efd76636b47002284e8152-f00dceebebf33fc3-01\n    [X-Amzn-Trace-Id] => Root=1-68ba1a4d-75efd76636b47002284e8152;Parent=f00dceebebf33fc3;Sampled=1\n    [X-Adsk-Signature] => sha256=6a1db2e29441acc429e5214b2f7462b6c23a953513f87870a322d9a91df95cfb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757026893919-569-71004061\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-04 23:01:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757026893919-569-71004061","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"569-71004061","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-09-04T23:01:33.919Z"},"publishedAt":"2025-09-04T23:01:33.000Z","csn":"5103159758"}
[webhook] [2025-09-04 23:01:37] [adwsapi_v2.php:20]  Webhook request received at 2025-09-04 23:01:37
[webhook] [2025-09-04 23:01:37] [adwsapi_v2.php:36]  Provided signature: sha256=5e0ac00499c7d5485ba0c6bd2a51ce7107a19ac0bb5bae0e2e13c8575a1a58ab
[webhook] [2025-09-04 23:01:37] [adwsapi_v2.php:37]  Calculated signature: sha256=6a1db2e29441acc429e5214b2f7462b6c23a953513f87870a322d9a91df95cfb
[webhook] [2025-09-04 23:01:37] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 03adf448c9f8bb8d\n    [X-B3-Traceid] => 68ba1a4d75efd76636b47002284e8152\n    [B3] => 68ba1a4d75efd76636b47002284e8152-03adf448c9f8bb8d-1\n    [Traceparent] => 00-68ba1a4d75efd76636b47002284e8152-03adf448c9f8bb8d-01\n    [X-Amzn-Trace-Id] => Root=1-68ba1a4d-75efd76636b47002284e8152;Parent=03adf448c9f8bb8d;Sampled=1\n    [X-Adsk-Signature] => sha256=5e0ac00499c7d5485ba0c6bd2a51ce7107a19ac0bb5bae0e2e13c8575a1a58ab\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757026893919-569-71004061\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-04 23:01:37] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757026893919-569-71004061","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"569-71004061","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-09-04T23:01:33.919Z"},"publishedAt":"2025-09-04T23:01:33.000Z","csn":"5103159758"}
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 00:01:18
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:36]  Provided signature: sha256=9884fc1811024691942abf6fd7db47511bb28bdf8784b3bc879edfac725264b4
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:37]  Calculated signature: sha256=8f0f3d2f6d90836f843f27c5d1da92fb45434cd4f9d5d5f9c88a6f4e5bdaad64
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6bca1b9cd94d3f60\n    [X-B3-Traceid] => 68ba284b71d892715a4932f67f278562\n    [B3] => 68ba284b71d892715a4932f67f278562-6bca1b9cd94d3f60-1\n    [Traceparent] => 00-68ba284b71d892715a4932f67f278562-6bca1b9cd94d3f60-01\n    [X-Amzn-Trace-Id] => Root=1-68ba284b-71d892715a4932f67f278562;Parent=6bca1b9cd94d3f60;Sampled=1\n    [X-Adsk-Signature] => sha256=9884fc1811024691942abf6fd7db47511bb28bdf8784b3bc879edfac725264b4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 986899da-309d-4956-82a3-62c427260614\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"986899da-309d-4956-82a3-62c427260614","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-09-05T00:01:15Z"},"publishedAt":"2025-09-05T00:01:15.000Z","country":"GB"}
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 00:01:18
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:36]  Provided signature: sha256=8f0f3d2f6d90836f843f27c5d1da92fb45434cd4f9d5d5f9c88a6f4e5bdaad64
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:37]  Calculated signature: sha256=8f0f3d2f6d90836f843f27c5d1da92fb45434cd4f9d5d5f9c88a6f4e5bdaad64
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 46e72beb9cb64f61\n    [X-B3-Traceid] => 68ba284b71d892715a4932f67f278562\n    [B3] => 68ba284b71d892715a4932f67f278562-46e72beb9cb64f61-1\n    [Traceparent] => 00-68ba284b71d892715a4932f67f278562-46e72beb9cb64f61-01\n    [X-Amzn-Trace-Id] => Root=1-68ba284b-71d892715a4932f67f278562;Parent=46e72beb9cb64f61;Sampled=1\n    [X-Adsk-Signature] => sha256=8f0f3d2f6d90836f843f27c5d1da92fb45434cd4f9d5d5f9c88a6f4e5bdaad64\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 986899da-309d-4956-82a3-62c427260614\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 00:01:18] [adwsapi_v2.php:57]  Received webhook data: {"id":"986899da-309d-4956-82a3-62c427260614","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-09-05T00:01:15Z"},"publishedAt":"2025-09-05T00:01:15.000Z","country":"GB"}
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 00:06:46
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:36]  Provided signature: sha256=dbefcec15200644ebb3b5e47c952323b4341ee63580fb82ad21c71651e2954da
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:37]  Calculated signature: sha256=917e1f6a2720c0ee9a4e622fa462a31454c98c4ab59e8e06d71fbccaedaf9a00
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3f7be42322621557\n    [X-B3-Traceid] => 68ba2994061610fe45749a157faaa170\n    [B3] => 68ba2994061610fe45749a157faaa170-3f7be42322621557-1\n    [Traceparent] => 00-68ba2994061610fe45749a157faaa170-3f7be42322621557-01\n    [X-Amzn-Trace-Id] => Root=1-68ba2994-061610fe45749a157faaa170;Parent=3f7be42322621557;Sampled=1\n    [X-Adsk-Signature] => sha256=dbefcec15200644ebb3b5e47c952323b4341ee63580fb82ad21c71651e2954da\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3e511d3f-7ca4-494e-8ac9-800e1a22a3b4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"3e511d3f-7ca4-494e-8ac9-800e1a22a3b4","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Price_Change","modifiedAt":"2025-09-05T00:06:44Z"},"publishedAt":"2025-09-05T00:06:44.000Z","country":"GB"}
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 00:06:46
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:36]  Provided signature: sha256=917e1f6a2720c0ee9a4e622fa462a31454c98c4ab59e8e06d71fbccaedaf9a00
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:37]  Calculated signature: sha256=917e1f6a2720c0ee9a4e622fa462a31454c98c4ab59e8e06d71fbccaedaf9a00
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 985071882c1183e3\n    [X-B3-Traceid] => 68ba2994061610fe45749a157faaa170\n    [B3] => 68ba2994061610fe45749a157faaa170-985071882c1183e3-1\n    [Traceparent] => 00-68ba2994061610fe45749a157faaa170-985071882c1183e3-01\n    [X-Amzn-Trace-Id] => Root=1-68ba2994-061610fe45749a157faaa170;Parent=985071882c1183e3;Sampled=1\n    [X-Adsk-Signature] => sha256=917e1f6a2720c0ee9a4e622fa462a31454c98c4ab59e8e06d71fbccaedaf9a00\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 3e511d3f-7ca4-494e-8ac9-800e1a22a3b4\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 00:06:46] [adwsapi_v2.php:57]  Received webhook data: {"id":"3e511d3f-7ca4-494e-8ac9-800e1a22a3b4","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Price_Change","modifiedAt":"2025-09-05T00:06:44Z"},"publishedAt":"2025-09-05T00:06:44.000Z","country":"GB"}
[webhook] [2025-09-05 06:34:26] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 06:34:26
[webhook] [2025-09-05 06:34:26] [adwsapi_v2.php:36]  Provided signature: sha256=f43794bcb63ee555fbd212cde07b5654365b2ca0641bb2c8367e916231c29dd5
[webhook] [2025-09-05 06:34:26] [adwsapi_v2.php:37]  Calculated signature: sha256=f43794bcb63ee555fbd212cde07b5654365b2ca0641bb2c8367e916231c29dd5
[webhook] [2025-09-05 06:34:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1e0955965503ef02\n    [X-B3-Traceid] => 68ba84703798ab010eee17c973386f3b\n    [B3] => 68ba84703798ab010eee17c973386f3b-1e0955965503ef02-1\n    [Traceparent] => 00-68ba84703798ab010eee17c973386f3b-1e0955965503ef02-01\n    [X-Amzn-Trace-Id] => Root=1-68ba8470-3798ab010eee17c973386f3b;Parent=1e0955965503ef02;Sampled=1\n    [X-Adsk-Signature] => sha256=f43794bcb63ee555fbd212cde07b5654365b2ca0641bb2c8367e916231c29dd5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7c80ac5c-466a-4f64-a4f8-3928272d82ba\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 06:34:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"7c80ac5c-466a-4f64-a4f8-3928272d82ba","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","status":"Expired","quantity":1,"message":"subscription status,quantity changed.","modifiedAt":"2025-09-05T05:39:30.000+0000"},"publishedAt":"2025-09-05T06:34:24.000Z","csn":"5103159758"}
[webhook] [2025-09-05 06:34:27] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 06:34:27
[webhook] [2025-09-05 06:34:27] [adwsapi_v2.php:36]  Provided signature: sha256=149ca48bf78d7974b8d913ea70bcd26430f9eae2b7a9c843d4015bbef9486f7f
[webhook] [2025-09-05 06:34:27] [adwsapi_v2.php:37]  Calculated signature: sha256=f43794bcb63ee555fbd212cde07b5654365b2ca0641bb2c8367e916231c29dd5
[webhook] [2025-09-05 06:34:27] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => de08afa0b87f38fc\n    [X-B3-Traceid] => 68ba84703798ab010eee17c973386f3b\n    [B3] => 68ba84703798ab010eee17c973386f3b-de08afa0b87f38fc-1\n    [Traceparent] => 00-68ba84703798ab010eee17c973386f3b-de08afa0b87f38fc-01\n    [X-Amzn-Trace-Id] => Root=1-68ba8470-3798ab010eee17c973386f3b;Parent=de08afa0b87f38fc;Sampled=1\n    [X-Adsk-Signature] => sha256=149ca48bf78d7974b8d913ea70bcd26430f9eae2b7a9c843d4015bbef9486f7f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7c80ac5c-466a-4f64-a4f8-3928272d82ba\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 06:34:27] [adwsapi_v2.php:57]  Received webhook data: {"id":"7c80ac5c-466a-4f64-a4f8-3928272d82ba","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","status":"Expired","quantity":1,"message":"subscription status,quantity changed.","modifiedAt":"2025-09-05T05:39:30.000+0000"},"publishedAt":"2025-09-05T06:34:24.000Z","csn":"5103159758"}
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 07:00:33
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:36]  Provided signature: sha256=5981ba91a317eb093793024bb022aa0043d2ebdeae51871e1c46c368481eba7d
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:37]  Calculated signature: sha256=db6c9b3f2d13421f5b5aad545a1fcb140fbedb925cb395e5145d292de3ebb97f
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f1798ccd52eeb3a4\n    [X-B3-Traceid] => 68ba8a8fac7fd818d10dd6ef1c370731\n    [B3] => 68ba8a8fac7fd818d10dd6ef1c370731-f1798ccd52eeb3a4-1\n    [Traceparent] => 00-68ba8a8fac7fd818d10dd6ef1c370731-f1798ccd52eeb3a4-01\n    [X-Amzn-Trace-Id] => Root=1-68ba8a8f-ac7fd818d10dd6ef1c370731;Parent=f1798ccd52eeb3a4;Sampled=1\n    [X-Adsk-Signature] => sha256=5981ba91a317eb093793024bb022aa0043d2ebdeae51871e1c46c368481eba7d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 201afdcb-f391-4f33-953b-8046c992f95e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"201afdcb-f391-4f33-953b-8046c992f95e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-761853","transactionId":"6d13451d-1fd0-5bef-85b0-258b697b47da","quoteStatus":"Expired","message":"Quote# Q-761853 status changed to Expired.","modifiedAt":"2025-09-05T07:00:31.300Z"},"publishedAt":"2025-09-05T07:00:31.000Z","csn":"5103159758"}
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 07:00:33
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:36]  Provided signature: sha256=db6c9b3f2d13421f5b5aad545a1fcb140fbedb925cb395e5145d292de3ebb97f
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:37]  Calculated signature: sha256=db6c9b3f2d13421f5b5aad545a1fcb140fbedb925cb395e5145d292de3ebb97f
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2cc60da6dea13710\n    [X-B3-Traceid] => 68ba8a8fac7fd818d10dd6ef1c370731\n    [B3] => 68ba8a8fac7fd818d10dd6ef1c370731-2cc60da6dea13710-1\n    [Traceparent] => 00-68ba8a8fac7fd818d10dd6ef1c370731-2cc60da6dea13710-01\n    [X-Amzn-Trace-Id] => Root=1-68ba8a8f-ac7fd818d10dd6ef1c370731;Parent=2cc60da6dea13710;Sampled=1\n    [X-Adsk-Signature] => sha256=db6c9b3f2d13421f5b5aad545a1fcb140fbedb925cb395e5145d292de3ebb97f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 201afdcb-f391-4f33-953b-8046c992f95e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 07:00:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"201afdcb-f391-4f33-953b-8046c992f95e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-761853","transactionId":"6d13451d-1fd0-5bef-85b0-258b697b47da","quoteStatus":"Expired","message":"Quote# Q-761853 status changed to Expired.","modifiedAt":"2025-09-05T07:00:31.300Z"},"publishedAt":"2025-09-05T07:00:31.000Z","csn":"5103159758"}
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 09:05:34
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:36]  Provided signature: sha256=c1c3dd57d1acab6aac9d281ce45be531e381706941381b64ea4e063334d6f739
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:37]  Calculated signature: sha256=c1c3dd57d1acab6aac9d281ce45be531e381706941381b64ea4e063334d6f739
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6b202bdb7c3339e1\n    [X-B3-Traceid] => 68baa7dc02be11931ace03d2389016cb\n    [B3] => 68baa7dc02be11931ace03d2389016cb-6b202bdb7c3339e1-1\n    [Traceparent] => 00-68baa7dc02be11931ace03d2389016cb-6b202bdb7c3339e1-01\n    [X-Amzn-Trace-Id] => Root=1-68baa7dc-02be11931ace03d2389016cb;Parent=6b202bdb7c3339e1;Sampled=1\n    [X-Adsk-Signature] => sha256=c1c3dd57d1acab6aac9d281ce45be531e381706941381b64ea4e063334d6f739\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 78fb5207-ee2b-4f23-bd12-52d10a136ed7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"78fb5207-ee2b-4f23-bd12-52d10a136ed7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","status":"Active","message":"subscription status changed.","modifiedAt":"2025-09-05T06:41:52.000+0000"},"publishedAt":"2025-09-05T09:05:32.000Z","csn":"5103159758"}
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 09:05:34
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:36]  Provided signature: sha256=2f2e96d854dce4d14c2155784d5caa0b9990ca12af4883bb44efd9780c47a94c
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:37]  Calculated signature: sha256=c1c3dd57d1acab6aac9d281ce45be531e381706941381b64ea4e063334d6f739
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 81beea6bfdff82d3\n    [X-B3-Traceid] => 68baa7dc02be11931ace03d2389016cb\n    [B3] => 68baa7dc02be11931ace03d2389016cb-81beea6bfdff82d3-1\n    [Traceparent] => 00-68baa7dc02be11931ace03d2389016cb-81beea6bfdff82d3-01\n    [X-Amzn-Trace-Id] => Root=1-68baa7dc-02be11931ace03d2389016cb;Parent=81beea6bfdff82d3;Sampled=1\n    [X-Adsk-Signature] => sha256=2f2e96d854dce4d14c2155784d5caa0b9990ca12af4883bb44efd9780c47a94c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 78fb5207-ee2b-4f23-bd12-52d10a136ed7\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 09:05:34] [adwsapi_v2.php:57]  Received webhook data: {"id":"78fb5207-ee2b-4f23-bd12-52d10a136ed7","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","status":"Active","message":"subscription status changed.","modifiedAt":"2025-09-05T06:41:52.000+0000"},"publishedAt":"2025-09-05T09:05:32.000Z","csn":"5103159758"}
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 10:07:22
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:36]  Provided signature: sha256=d31ec4243ad9f8b1f83d8160a8cf1650e3fc2e58d87f1b4c5fbde85c647accf4
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:37]  Calculated signature: sha256=0226a6498d9b2f17cdce6ca4b01765f9fef2af07ace1904350aa38c6433c83ee
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 299e5b1358097ff6\n    [X-B3-Traceid] => 68bab657feb84c78f7b5ca2e780a38be\n    [B3] => 68bab657feb84c78f7b5ca2e780a38be-299e5b1358097ff6-1\n    [Traceparent] => 00-68bab657feb84c78f7b5ca2e780a38be-299e5b1358097ff6-01\n    [X-Amzn-Trace-Id] => Root=1-68bab657-feb84c78f7b5ca2e780a38be;Parent=299e5b1358097ff6;Sampled=1\n    [X-Adsk-Signature] => sha256=d31ec4243ad9f8b1f83d8160a8cf1650e3fc2e58d87f1b4c5fbde85c647accf4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 83dfc732-8a53-4fd8-a3fd-16730c2b11ed\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"83dfc732-8a53-4fd8-a3fd-16730c2b11ed","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1064179","transactionId":"1c823af9-d795-5fd1-ab25-5bcd6f01dbf7","quoteStatus":"Draft","message":"Quote# Q-1064179 status changed to Draft.","modifiedAt":"2025-09-05T10:07:19.145Z"},"publishedAt":"2025-09-05T10:07:19.000Z","csn":"5103159758"}
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 10:07:22
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:36]  Provided signature: sha256=0226a6498d9b2f17cdce6ca4b01765f9fef2af07ace1904350aa38c6433c83ee
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:37]  Calculated signature: sha256=0226a6498d9b2f17cdce6ca4b01765f9fef2af07ace1904350aa38c6433c83ee
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1480ebf2e008a9a0\n    [X-B3-Traceid] => 68bab657feb84c78f7b5ca2e780a38be\n    [B3] => 68bab657feb84c78f7b5ca2e780a38be-1480ebf2e008a9a0-1\n    [Traceparent] => 00-68bab657feb84c78f7b5ca2e780a38be-1480ebf2e008a9a0-01\n    [X-Amzn-Trace-Id] => Root=1-68bab657-feb84c78f7b5ca2e780a38be;Parent=1480ebf2e008a9a0;Sampled=1\n    [X-Adsk-Signature] => sha256=0226a6498d9b2f17cdce6ca4b01765f9fef2af07ace1904350aa38c6433c83ee\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 83dfc732-8a53-4fd8-a3fd-16730c2b11ed\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 10:07:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"83dfc732-8a53-4fd8-a3fd-16730c2b11ed","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1064179","transactionId":"1c823af9-d795-5fd1-ab25-5bcd6f01dbf7","quoteStatus":"Draft","message":"Quote# Q-1064179 status changed to Draft.","modifiedAt":"2025-09-05T10:07:19.145Z"},"publishedAt":"2025-09-05T10:07:19.000Z","csn":"5103159758"}
[webhook] [2025-09-05 10:09:32] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 10:09:32
[webhook] [2025-09-05 10:09:32] [adwsapi_v2.php:36]  Provided signature: sha256=185b4b722baeabfd12b2676f372f04d86be5f7acc7fd4bf08a1ec9a4a9daeb99
[webhook] [2025-09-05 10:09:32] [adwsapi_v2.php:37]  Calculated signature: sha256=185b4b722baeabfd12b2676f372f04d86be5f7acc7fd4bf08a1ec9a4a9daeb99
[webhook] [2025-09-05 10:09:32] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e14328b0a594280f\n    [X-B3-Traceid] => 68bab6da139840adc239c87d1ccf19c3\n    [B3] => 68bab6da139840adc239c87d1ccf19c3-e14328b0a594280f-1\n    [Traceparent] => 00-68bab6da139840adc239c87d1ccf19c3-e14328b0a594280f-01\n    [X-Amzn-Trace-Id] => Root=1-68bab6da-139840adc239c87d1ccf19c3;Parent=e14328b0a594280f;Sampled=1\n    [X-Adsk-Signature] => sha256=185b4b722baeabfd12b2676f372f04d86be5f7acc7fd4bf08a1ec9a4a9daeb99\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9f7735b4-50ef-4341-b7cb-293807562cd9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 10:09:32] [adwsapi_v2.php:57]  Received webhook data: {"id":"9f7735b4-50ef-4341-b7cb-293807562cd9","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1064179","transactionId":"1c823af9-d795-5fd1-ab25-5bcd6f01dbf7","quoteStatus":"Quoted","message":"Quote# Q-1064179 status changed to Quoted.","modifiedAt":"2025-09-05T10:09:30.413Z"},"publishedAt":"2025-09-05T10:09:30.000Z","csn":"5103159758"}
[webhook] [2025-09-05 10:09:33] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 10:09:33
[webhook] [2025-09-05 10:09:33] [adwsapi_v2.php:36]  Provided signature: sha256=971b9bb7234b10b3cc3e9a1be5628f07f19dc5a6dac4820e54d5a763d1e2fa95
[webhook] [2025-09-05 10:09:33] [adwsapi_v2.php:37]  Calculated signature: sha256=185b4b722baeabfd12b2676f372f04d86be5f7acc7fd4bf08a1ec9a4a9daeb99
[webhook] [2025-09-05 10:09:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 56c49a8b4ef136b8\n    [X-B3-Traceid] => 68bab6da139840adc239c87d1ccf19c3\n    [B3] => 68bab6da139840adc239c87d1ccf19c3-56c49a8b4ef136b8-1\n    [Traceparent] => 00-68bab6da139840adc239c87d1ccf19c3-56c49a8b4ef136b8-01\n    [X-Amzn-Trace-Id] => Root=1-68bab6da-139840adc239c87d1ccf19c3;Parent=56c49a8b4ef136b8;Sampled=1\n    [X-Adsk-Signature] => sha256=971b9bb7234b10b3cc3e9a1be5628f07f19dc5a6dac4820e54d5a763d1e2fa95\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9f7735b4-50ef-4341-b7cb-293807562cd9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 10:09:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"9f7735b4-50ef-4341-b7cb-293807562cd9","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1064179","transactionId":"1c823af9-d795-5fd1-ab25-5bcd6f01dbf7","quoteStatus":"Quoted","message":"Quote# Q-1064179 status changed to Quoted.","modifiedAt":"2025-09-05T10:09:30.413Z"},"publishedAt":"2025-09-05T10:09:30.000Z","csn":"5103159758"}
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 12:16:33
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:36]  Provided signature: sha256=827be45a6cab92dbd34ea0ce20061137feaa0fb2dcb676913e3f284152bfdc3c
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:37]  Calculated signature: sha256=827be45a6cab92dbd34ea0ce20061137feaa0fb2dcb676913e3f284152bfdc3c
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8e3e1ace70e20c1d\n    [X-B3-Traceid] => 68bad49f3e3a52b81f3196c579954e87\n    [B3] => 68bad49f3e3a52b81f3196c579954e87-8e3e1ace70e20c1d-1\n    [Traceparent] => 00-68bad49f3e3a52b81f3196c579954e87-8e3e1ace70e20c1d-01\n    [X-Amzn-Trace-Id] => Root=1-68bad49f-3e3a52b81f3196c579954e87;Parent=8e3e1ace70e20c1d;Sampled=1\n    [X-Adsk-Signature] => sha256=827be45a6cab92dbd34ea0ce20061137feaa0fb2dcb676913e3f284152bfdc3c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8d00e183-d661-4ffe-ba07-3247f3c16346\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"8d00e183-d661-4ffe-ba07-3247f3c16346","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55569793860285","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-09-05T09:08:51.000+0000"},"publishedAt":"2025-09-05T12:16:31.000Z","csn":"5103159758"}
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 12:16:33
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:36]  Provided signature: sha256=60c7b2d8af0a8940b74d96f1a7a0350effe5618919099c00039e10cdb45c5af0
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:37]  Calculated signature: sha256=827be45a6cab92dbd34ea0ce20061137feaa0fb2dcb676913e3f284152bfdc3c
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => be131b0f04c19e51\n    [X-B3-Traceid] => 68bad49f3e3a52b81f3196c579954e87\n    [B3] => 68bad49f3e3a52b81f3196c579954e87-be131b0f04c19e51-1\n    [Traceparent] => 00-68bad49f3e3a52b81f3196c579954e87-be131b0f04c19e51-01\n    [X-Amzn-Trace-Id] => Root=1-68bad49f-3e3a52b81f3196c579954e87;Parent=be131b0f04c19e51;Sampled=1\n    [X-Adsk-Signature] => sha256=60c7b2d8af0a8940b74d96f1a7a0350effe5618919099c00039e10cdb45c5af0\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8d00e183-d661-4ffe-ba07-3247f3c16346\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 12:16:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"8d00e183-d661-4ffe-ba07-3247f3c16346","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"55569793860285","autoRenew":"OFF","message":"subscription autoRenew changed.","modifiedAt":"2025-09-05T09:08:51.000+0000"},"publishedAt":"2025-09-05T12:16:31.000Z","csn":"5103159758"}
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 12:55:42
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:36]  Provided signature: sha256=219f645ccec4c43cb3ca41dced819b141f16223cdfa9f8adbb967c2e0cbf9d38
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:37]  Calculated signature: sha256=219f645ccec4c43cb3ca41dced819b141f16223cdfa9f8adbb967c2e0cbf9d38
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7c81d49daa452b44\n    [X-B3-Traceid] => 68baddcc2a3fb5a21d6015906300a991\n    [B3] => 68baddcc2a3fb5a21d6015906300a991-7c81d49daa452b44-1\n    [Traceparent] => 00-68baddcc2a3fb5a21d6015906300a991-7c81d49daa452b44-01\n    [X-Amzn-Trace-Id] => Root=1-68baddcc-2a3fb5a21d6015906300a991;Parent=7c81d49daa452b44;Sampled=1\n    [X-Adsk-Signature] => sha256=219f645ccec4c43cb3ca41dced819b141f16223cdfa9f8adbb967c2e0cbf9d38\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757076940372-66065226907420-9033746879-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757076940372-66065226907420-9033746879-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-05T12:55:40.372Z"},"publishedAt":"2025-09-05T12:55:40.000Z","csn":"5103159758"}
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 12:55:42
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:36]  Provided signature: sha256=3db85fe102bea6e1c40fac920bfc8768f80e7dec481239b42ce5d92c1a8c731f
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:37]  Calculated signature: sha256=219f645ccec4c43cb3ca41dced819b141f16223cdfa9f8adbb967c2e0cbf9d38
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a11e89e4cc6836d6\n    [X-B3-Traceid] => 68baddcc2a3fb5a21d6015906300a991\n    [B3] => 68baddcc2a3fb5a21d6015906300a991-a11e89e4cc6836d6-1\n    [Traceparent] => 00-68baddcc2a3fb5a21d6015906300a991-a11e89e4cc6836d6-01\n    [X-Amzn-Trace-Id] => Root=1-68baddcc-2a3fb5a21d6015906300a991;Parent=a11e89e4cc6836d6;Sampled=1\n    [X-Adsk-Signature] => sha256=3db85fe102bea6e1c40fac920bfc8768f80e7dec481239b42ce5d92c1a8c731f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1757076940372-66065226907420-9033746879-1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 12:55:42] [adwsapi_v2.php:57]  Received webhook data: {"id":"1757076940372-66065226907420-9033746879-1","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"66065226907420","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-09-05T12:55:40.372Z"},"publishedAt":"2025-09-05T12:55:40.000Z","csn":"5103159758"}
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 13:33:48
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:36]  Provided signature: sha256=198cb66efdde8b7ca3a27e1605e55f1ec92036904483531dc1d9ce6ca2216b76
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:37]  Calculated signature: sha256=198cb66efdde8b7ca3a27e1605e55f1ec92036904483531dc1d9ce6ca2216b76
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f7c57ba6c84e95bd\n    [X-B3-Traceid] => 68bae6ba6b3b564866b2de8a6b73c1cc\n    [B3] => 68bae6ba6b3b564866b2de8a6b73c1cc-f7c57ba6c84e95bd-1\n    [Traceparent] => 00-68bae6ba6b3b564866b2de8a6b73c1cc-f7c57ba6c84e95bd-01\n    [X-Amzn-Trace-Id] => Root=1-68bae6ba-6b3b564866b2de8a6b73c1cc;Parent=f7c57ba6c84e95bd;Sampled=1\n    [X-Adsk-Signature] => sha256=198cb66efdde8b7ca3a27e1605e55f1ec92036904483531dc1d9ce6ca2216b76\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4fcff3fb-d0a1-4ee7-9307-62f86009f3d0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"4fcff3fb-d0a1-4ee7-9307-62f86009f3d0","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73341386174790","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-09-05T10:45:04.000+0000"},"publishedAt":"2025-09-05T13:33:46.000Z","csn":"5103159758"}
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:20]  Webhook request received at 2025-09-05 13:33:48
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:36]  Provided signature: sha256=a7e3347b210d3645e0dce44fd584752c74d804313944862928df1fac4a394f4a
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:37]  Calculated signature: sha256=198cb66efdde8b7ca3a27e1605e55f1ec92036904483531dc1d9ce6ca2216b76
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0c3c043ca71fe6f4\n    [X-B3-Traceid] => 68bae6ba6b3b564866b2de8a6b73c1cc\n    [B3] => 68bae6ba6b3b564866b2de8a6b73c1cc-0c3c043ca71fe6f4-1\n    [Traceparent] => 00-68bae6ba6b3b564866b2de8a6b73c1cc-0c3c043ca71fe6f4-01\n    [X-Amzn-Trace-Id] => Root=1-68bae6ba-6b3b564866b2de8a6b73c1cc;Parent=0c3c043ca71fe6f4;Sampled=1\n    [X-Adsk-Signature] => sha256=a7e3347b210d3645e0dce44fd584752c74d804313944862928df1fac4a394f4a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 4fcff3fb-d0a1-4ee7-9307-62f86009f3d0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-09-05 13:33:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"4fcff3fb-d0a1-4ee7-9307-62f86009f3d0","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"73341386174790","quantity":1,"autoRenew":"OFF","message":"subscription quantity,autoRenew changed.","modifiedAt":"2025-09-05T10:45:04.000+0000"},"publishedAt":"2025-09-05T13:33:46.000Z","csn":"5103159758"}
