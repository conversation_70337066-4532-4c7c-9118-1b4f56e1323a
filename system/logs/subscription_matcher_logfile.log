[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:101] Company search for 'Conrad Energy Ltd': long_terms=["conrad","energy"], all_terms=["conrad","energy"]
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:114] Using OR logic for long terms: (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%conrad%","%energy%"]
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"Conrad Energy Ltd","limit":200}
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","hx-swap":"innerHTML","hx-target":"#modal_body","hx-vals":"{\"csn\":\"endcust_account_csn\",\"subscription_number\":\"subs_subscriptionReferenceNumber\"}","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","hx-swap":"innerHTML","hx-target":"#modal_body","hx-vals":"{\"csn\":\"endcust_account_csn\",\"subscription_number\":\"subs_subscriptionReferenceNumber\"}","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 09:45:17"}
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 10:18:25] [subscription_matcher.class.php:493] Total CSV entries found: 0
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:101] Company search for 'POQ LTD': long_terms=[], all_terms=["poq"]
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:126] Using AND logic for short terms: (LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%poq%"]
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"POQ LTD","limit":200}
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","hx-swap":"innerHTML","hx-target":"#modal_body","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","hx-swap":"innerHTML","hx-target":"#modal_body","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 10:41:02"}
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 10:41:05] [subscription_matcher.class.php:493] Total CSV entries found: 0
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:101] Company search for 'Proactive Learning Ltd': long_terms=["proactive","learning"], all_terms=["proactive","learning"]
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:114] Using OR logic for long terms: (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%proactive%","%learning%"]
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"Proactive Learning Ltd","limit":200}
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 11:34:11"}
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 12:43:03] [subscription_matcher.class.php:493] Total CSV entries found: 0
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:101] Company search for 'Camland Developments Ltd': long_terms=["camland","developments"], all_terms=["camland","developments"]
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:114] Using OR logic for long terms: (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%camland%","%developments%"]
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"Camland Developments Ltd","limit":200}
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 11:34:11"}
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 12:47:17] [subscription_matcher.class.php:493] Total CSV entries found: 0
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:101] Company search for 'STRATEGIC PM SOLUTIONS Ltd': long_terms=["strategic"], all_terms=["strategic","pm"]
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:114] Using OR logic for long terms: (LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%strategic%"]
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"STRATEGIC PM SOLUTIONS Ltd","limit":200}
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 11:34:11"}
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:00:39] [subscription_matcher.class.php:493] Total CSV entries found: 0
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:101] Company search for 'Conrad Energy Ltd': long_terms=["conrad","energy"], all_terms=["conrad","energy"]
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:114] Using OR logic for long terms: (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%conrad%","%energy%"]
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"Conrad Energy Ltd","limit":200}
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 11:34:11"}
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:01:18] [subscription_matcher.class.php:493] Total CSV entries found: 0
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:101] Company search for 'Conrad Energy Ltd': long_terms=["conrad","energy"], all_terms=["conrad","energy"]
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:114] Using OR logic for long terms: (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ? OR LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%conrad%","%energy%"]
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"Conrad Energy Ltd","limit":200}
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 11:34:11"}
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:01:22] [subscription_matcher.class.php:493] Total CSV entries found: 0
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:101] Company search for 'STRATEGIC PM SOLUTIONS Ltd': long_terms=["strategic"], all_terms=["strategic","pm"]
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:114] Using OR logic for long terms: (LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%strategic%"]
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"STRATEGIC PM SOLUTIONS Ltd","limit":200}
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 11:34:11"}
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:03:22] [subscription_matcher.class.php:493] Total CSV entries found: 0
[subscription_matcher] [2025-09-05 13:28:11] [subscription_matcher.class.php:101] Company search for 'STRATEGIC PM SOLUTIONS Ltd': long_terms=["strategic"], all_terms=["strategic","pm"]
[subscription_matcher] [2025-09-05 13:28:11] [subscription_matcher.class.php:114] Using OR logic for long terms: (LOWER(company_name) LIKE ?)
[subscription_matcher] [2025-09-05 13:28:11] [subscription_matcher.class.php:130] Executing query: SELECT * FROM manual_subscription_entries WHERE (LOWER(company_name) LIKE ?) ORDER BY created_at DESC LIMIT 200 with params: ["%strategic%"]
[subscription_matcher] [2025-09-05 13:28:11] [subscription_matcher.class.php:132] Found 0 raw matches before similarity filtering
[subscription_matcher] [2025-09-05 13:28:11] [subscription_matcher.class.php:145] After similarity filtering (≥30%): 0 matches remain
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:375] Starting CSV table search with criteria: {"search":"STRATEGIC PM SOLUTIONS Ltd","limit":200}
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:385] Checking table: autobooks_import_sketchup_data, config: {"structure":[{"id":"col_5_c1007e8ab1238e90f79100fbfff39635","label":"Company Name","field":"company_name","fields":["company_name","email","contact_name"],"filter":true,"visible":true},{"id":"col_18_f5625f45195c610c4ab60fdc118a2cdb","label":"Product Name","field":"product_name","fields":["product_name"],"filter":true,"visible":true},{"id":"col_9_4ed5d2eaed1a1fadcc41ad1d58ed603e","label":"City","field":"city","fields":[],"filter":true,"visible":true},{"id":"col_6_884d9804999fc47a3c2694e49ad2536a","label":"Address","field":"address","fields":["address","city","postal_code"],"filter":true,"visible":true},{"id":"col_11_e601d3510ec11035be5f52bd80308996","label":"Postal Code","field":"postal_code","fields":[],"filter":true,"visible":true},{"id":"col_0_855a49c592df215a4abe977e4b4ebe1e","label":"Sold To Name","field":"sold_to_name","fields":["sold_to_name"],"filter":true,"visible":true},{"id":"col_1_1a78581b905a2b8b3819cda087a681df","label":"Sold To Number","field":"sold_to_number","fields":["sold_to_number"],"filter":true,"visible":true},{"id":"col_2_0ec1da7f1dc2f8abef07b11a3193ef57","label":"Vendor Name","field":"vendor_name","fields":["vendor_name"],"filter":true,"visible":true},{"id":"col_3_2582ec59e97c6010032089b939c5ba6b","label":"Reseller Name","field":"reseller_name","fields":["reseller_name"],"filter":true,"visible":true},{"id":"col_4_96b1f972094b863cafcacb8fc48b9bea","label":"Vendor Id","field":"vendor_id","fields":["vendor_id"],"filter":true,"visible":true},{"id":"col_7_ea92edd1525cbbe624e2a16347e81e08","label":"End Customer Address 2","field":"end_customer_address_2","fields":["end_customer_address_2"],"filter":true,"visible":true},{"id":"col_8_fd89e44f39c29b2e6e5ee8afcf84fe01","label":"End Customer Address 3","field":"end_customer_address_3","fields":["end_customer_address_3"],"filter":true,"visible":true},{"id":"col_10_9ed39e2ea931586b6a985a6942ef573e","label":"State","field":"state","fields":["state"],"filter":true,"visible":true},{"id":"col_12_e909c2d7067ea37437cf97fe11d91bd0","label":"Country","field":"country","fields":["country"],"filter":true,"visible":true},{"id":"col_13_8915939119a19af6a75ea0edf6663bba","label":"End Customer Account Type","field":"end_customer_account_type","fields":["end_customer_account_type"],"filter":true,"visible":true},{"id":"col_14_f788aab54dfc2dd451e5d46c1471010b","label":"Contact Name","field":"contact_name","fields":[],"filter":true,"visible":true},{"id":"col_15_0c83f57c786a0b4a39efab23731c7ebc","label":"Email","field":"email","fields":[],"filter":true,"visible":true},{"id":"col_16_948bf27ad83a985d151cfdedc2fd4adf","label":"End Customer Contact Phone","field":"end_customer_contact_phone","fields":["end_customer_contact_phone"],"filter":true,"visible":true},{"id":"col_17_c4aaf1d01f58e409021771db8b5bcc31","label":"End Customer Industry Segment","field":"end_customer_industry_segment","fields":["end_customer_industry_segment"],"filter":true,"visible":true},{"id":"col_19_85cd34111e239092df4564f2f2018f8e","label":"Subscription Reference","field":"subscription_reference","fields":["subscription_reference"],"filter":true,"visible":true},{"id":"col_20_7aa82fd5d0da58c82c3be193567003bd","label":"Sketchup Agreement Start Date","field":"sketchup_agreement_start_date","fields":["sketchup_agreement_start_date"],"filter":true,"visible":true},{"id":"col_21_5c0a26664568598b782e92b3e5e0a1d7","label":"Sketchup Agreement End Date","field":"sketchup_agreement_end_date","fields":["sketchup_agreement_end_date"],"filter":true,"visible":true},{"id":"col_22_2d017dd473b0b2349dceb1d7f33e3e44","label":"Agreement Terms","field":"agreement_terms","fields":["agreement_terms"],"filter":true,"visible":true},{"id":"col_23_8af78a35c6e73e79bf51fc8563b871fe","label":"Agreement Type","field":"agreement_type","fields":["agreement_type"],"filter":true,"visible":true},{"id":"col_24_adb05f2097fd47dcf014a7b073b34987","label":"Agreement Status","field":"agreement_status","fields":["agreement_status"],"filter":true,"visible":true},{"id":"col_25_b4d565e162b8e7bc13cb12729aae502a","label":"Agreement Support Level","field":"agreement_support_level","fields":["agreement_support_level"],"filter":true,"visible":true},{"id":"col_26_cb2af54b3de4924bea84de68a4f3b7bc","label":"Sketchup Agreement Days Due","field":"sketchup_agreement_days_due","fields":["sketchup_agreement_days_due"],"filter":true,"visible":true},{"id":"col_27_dc9f96b1aa4bec3da65d35e6f1575e35","label":"Sketchup Agreement Autorenew","field":"sketchup_agreement_autorenew","fields":["sketchup_agreement_autorenew"],"filter":true,"visible":true},{"id":"col_28_9faa73b68c2cefc1ed19820000ed6335","label":"Sketchup Product Name","field":"sketchup_product_name","fields":["sketchup_product_name"],"filter":true,"visible":true},{"id":"col_29_31981304965b1a7ee49c64d093e02e94","label":"Product Family","field":"product_family","fields":["product_family"],"filter":true,"visible":true},{"id":"col_30_7d11591eed252e3b1e06c3c8b170dd38","label":"Product Market Segment","field":"product_market_segment","fields":["product_market_segment"],"filter":true,"visible":true},{"id":"col_31_bce24ac7f3e20a9f1b05fe7df18981f9","label":"Sketchup Product Release","field":"sketchup_product_release","fields":["sketchup_product_release"],"filter":true,"visible":true},{"id":"col_32_b1d9df66b969ebda1ccfb1be323f165b","label":"Product Type","field":"product_type","fields":["product_type"],"filter":true,"visible":true},{"id":"col_33_a4ec132ba158d45a164578a02f6ef646","label":"Product Deployment","field":"product_deployment","fields":["product_deployment"],"filter":true,"visible":true},{"id":"col_34_197d90cc45a4687eb1ef153ac86bc05f","label":"Product Sku","field":"product_sku","fields":["product_sku"],"filter":true,"visible":true},{"id":"col_35_b487a1edb0f07fa54d48393bbfea8139","label":"Product Sku Description","field":"product_sku_description","fields":["product_sku_description"],"filter":true,"visible":true},{"id":"col_36_fdff8146fc34b8e8e8ee5abe920d4806","label":"Product Part","field":"product_part","fields":["product_part"],"filter":true,"visible":true},{"id":"col_37_ceefc2cd7f84cf31ee379912ce351ad3","label":"Product List Price","field":"product_list_price","fields":["product_list_price"],"filter":true,"visible":true},{"id":"col_38_5ecf81cda77fa7d258afcc5fd1c03928","label":"Sketchup Product List Price Currency","field":"sketchup_product_list_price_currency","fields":["sketchup_product_list_price_currency"],"filter":true,"visible":true},{"id":"col_39_e512284df8b40362b6434996edd28f11","label":"Sketchup Subscription Id","field":"sketchup_subscription_id","fields":["sketchup_subscription_id"],"filter":true,"visible":true},{"id":"col_40_8c20c72b0daef191013b97458e02bb25","label":"Subscription Serial Number","field":"subscription_serial_number","fields":["subscription_serial_number"],"filter":true,"visible":true},{"id":"col_41_9acb44549b41563697bb490144ec6258","label":"Status","field":"status","fields":["status"],"filter":true,"visible":true},{"id":"col_42_221d2a4bfdae13dbd5aeff3b02adb8c1","label":"Quantity","field":"quantity","fields":["quantity"],"filter":true,"visible":true},{"id":"col_43_860a15947861bb39c1391f2d492d5358","label":"Sketchup Subscription Start Date","field":"sketchup_subscription_start_date","fields":["sketchup_subscription_start_date"],"filter":true,"visible":true},{"id":"col_44_7fe83bbb17a7b62c45392c73f19abc01","label":"Sketchup Subscription End Date","field":"sketchup_subscription_end_date","fields":["sketchup_subscription_end_date"],"filter":true,"visible":true},{"id":"col_45_5fdcafa9911363ca87918c99782fea0c","label":"End Customer Contact Name","field":"end_customer_contact_name","fields":["end_customer_contact_name"],"filter":true,"visible":true},{"id":"col_46_a0394474520cb159560eeed0b4ff4b48","label":"Sketchup Subscription Contact Email","field":"sketchup_subscription_contact_email","fields":["sketchup_subscription_contact_email"],"filter":true,"visible":true},{"id":"col_47_aa1d9b48f2e7b9108127d34afdb14da8","label":"Subscription Level","field":"subscription_level","fields":["subscription_level"],"filter":true,"visible":true},{"id":"col_48_106cc0198e540fa33b18fe6cab86c1fb","label":"Sketchup Subscription Days Due","field":"sketchup_subscription_days_due","fields":["sketchup_subscription_days_due"],"filter":true,"visible":true},{"id":"col_49_2b6213d56e0299b7b5a352f7337328d8","label":"Quotation Id","field":"quotation_id","fields":["quotation_id"],"filter":true,"visible":true},{"id":"col_50_737c3c888ee0cc2600a2dc0d7f55ed05","label":"Quotation Type","field":"quotation_type","fields":["quotation_type"],"filter":true,"visible":true},{"id":"col_51_e3208176c7c17535ac26e38799521446","label":"Sketchup Quotation Vendor Id","field":"sketchup_quotation_vendor_id","fields":["sketchup_quotation_vendor_id"],"filter":true,"visible":true},{"id":"col_52_93afc8a705e7a5a614bea0352947afdf","label":"Sketchup Quotation Deal Registration Number","field":"sketchup_quotation_deal_registration_number","fields":["sketchup_quotation_deal_registration_number"],"filter":true,"visible":true},{"id":"col_53_a52a77131c467520565de2fb0c065189","label":"Quotation Status","field":"quotation_status","fields":["quotation_status"],"filter":true,"visible":true},{"id":"col_54_37a141c1009df18f37e8fb4c0b6b6b09","label":"Sketchup Quotation Resellerpo Previous","field":"sketchup_quotation_resellerpo_previous","fields":["sketchup_quotation_resellerpo_previous"],"filter":true,"visible":true},{"id":"col_55_9d5db5fa76b9a647a4828be35b2a5c8a","label":"Sketchup Quotation Due Date","field":"sketchup_quotation_due_date","fields":["sketchup_quotation_due_date"],"filter":true,"visible":true},{"id":"col_56_1180a372ed0e4f6d0f6d1babd14e28fb","label":"Flaer Phase","field":"flaer_phase","fields":["flaer_phase"],"filter":true,"visible":true},{"id":"col_57_b3dbde818a17fb81a2a610f0657778fa","label":"Sketchup Updated","field":"sketchup_updated","fields":["sketchup_updated"],"filter":true,"visible":true}],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":79,"created_at":"2025-08-26 10:59:11"}
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:385] Checking table: autodesk_customers, config: {"hidden":[],"structure":[{"id":"col_0_1c76cbfe21c6f44c1d1e59d54f3e4420","label":"Company","fields":["endcust_name","endcust_email"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_1_ce26601dac0dea138b7295f02b7620a7","label":"Customer","fields":["endcust_first_name","endcust_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_2_adc4365ac462ffb7ae7f47f348acbad4","label":"CSN","fields":["endcust_account_csn"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_3_9f6d3c18b7c7c1826c2e6b4b824ac5cf","label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_4_ce5bf551379459c1c61d2a204061c455","label":"Location","fields":["endcust_city","endcust_postal_code"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_5_84ba4cf46c874b0b5951eb3b75298329","label":"Last Modified","fields":["endcust_last_modified"],"filter":false,"visible":true,"action_buttons":[]},{"id":"col_6_06df33001c1d7187fdd81ea1f5b277aa","label":"Actions","fields":[null],"filter":false,"visible":true,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}]}],"columns":[{"label":"Company","fields":["endcust_name","endcust_email"],"selected":"Company"},{"label":"Customer","fields":["endcust_first_name","endcust_last_name"],"selected":"Customer"},{"label":"CSN","fields":["endcust_account_csn"],"selected":"CSN"},{"label":"Primary Admin","fields":["endcust_primary_admin_first_name","endcust_primary_admin_last_name"],"selected":"Primary Admin"},{"label":"Location","fields":["endcust_city","endcust_postal_code"],"selected":"Location"},{"label":"Last Modified","fields":["endcust_last_modified"],"selected":"Last Modified"},{"label":"Actions","filter":false,"action_buttons":[{"id":"view_customer","template":"action-customer-view-button","field":"endcust_account_csn","icon":"book-open","label":"View","hx-post":"\/baffletrain\/autocadlt\/autobooks\/api\/view","modal_trigger":true,"data_fields":{"csn":"endcust_account_csn","tab_title_template":"Customer {endcust_account_csn}","subscription_number":"subs_subscriptionReferenceNumber"}}],"selected":"Actions"}],"data_source_type":"hardcoded","data_source_id":null,"created_at":"2025-09-05 11:34:11"}
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:399] Found 0 CSV tables from unified system
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:406] No unified tables found, trying legacy system
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:415] Found 1 legacy CSV tables
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:424] Added legacy CSV table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:448] Processing 1 CSV tables for subscription data
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:452] Processing table: autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:470] Available fields for autobooks_import_sketchup_data: ["id","sold_to_name","sold_to_number","vendor_name","reseller_number","reseller_vendor_id","end_customer_vendor_id","end_customer_name","end_customer_address_1","end_customer_address_2","end_customer_address_3","end_customer_city","end_customer_state","end_customer_zip_code","end_customer_country","end_customer_account_type","end_customer_contact_name","end_customer_contact_email","end_customer_contact_phone","end_customer_industry_segment","agreement_program_name","agreement_number","agreement_start_date","agreement_end_date","agreement_terms","agreement_type","agreement_status","agreement_support_level","agreement_days_due","agreement_autorenew","product_name","product_family","product_market_segment","product_release","product_type","product_deployment","product_sku","product_sku_description","product_part","product_list_price","product_list_price_currency","subscription_id","subscription_serial_number","subscription_status","subscription_quantity","subscription_start_date","subscription_end_date","subscription_contact_name","subscription_contact_email","subscription_level","subscription_days_due","quotation_id","quotation_type","quotation_vendor_id","quotation_deal_registration_number","quotation_status","quotation_resellerpo_previous","quotation_due_date","flaer_phase","updated","created_at","updated_at"]
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:474] Table autobooks_import_sketchup_data contains subscription data, searching...
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:478] Found 0 entries in autobooks_import_sketchup_data
[subscription_matcher] [2025-09-05 13:28:12] [subscription_matcher.class.php:493] Total CSV entries found: 0
