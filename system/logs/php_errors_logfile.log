[php_errors] [2025-09-04 07:58:57] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 07:58:57] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 08:11:21] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 08:11:21] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 09:07:25] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 09:07:25] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 10:49:58] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:24:12] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:24:12] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:30:16] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:30:16] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:37:51] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:37:51] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:47:15] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:47:15] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:48:48] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 12:48:48] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 13:09:19] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 13:09:19] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-09-04 13:09:20] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 13:09:20] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-09-04 14:00:50] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 14:00:50] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 14:02:35] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 14:02:35] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 14:04:35] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 14:04:35] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 15:39:56] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 15:39:56] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-09-04 15:40:00] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 15:40:00] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-09-04 15:41:54] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 15:41:54] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-09-04 15:41:55] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 15:41:55] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-09-04 19:36:02] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 19:36:02] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-09-04 19:36:02] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-04 19:36:02] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(26) "Array to string conversion"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(103)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Array to string conversion","file":"adwsapi_v2.php","line":103,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 103\n         <strong>Arguments:</strong> \n         0: 2\n         1: "Array to string conversion"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 103\n-->\n
[php_errors] [2025-09-05 09:23:06] [functions.php:243] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "Exception"\n  ["message"]: string(37) "Field name and column ID are required"\n  ["file"]: string(26) "column_preferences.api.php"\n  ["line"]: int(273)\n  ["full_file"]: string(123) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php"\n  ["trace"]: string(268) "#0 sys_layout_layout-api.edge.php(98): api\data_table\column_preferences\remove_field_from_column()\n#1 edge.class.php(171): include()\n#2 edge.class.php(164): edge\edge::phprender()\n#3 router.class.php(211): edge\edge::render()\n#4 index.php(31): system\router::route()\n"\n  ["request_uri"]: string(91) "/baffletrain/autocadlt/autobooks/api/data_table/column_preferences/remove_field_from_column"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 243\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"Exception","message":"Field name and column ID are required","file":"column_preferences.api.php","line":273,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table\/column_preferences.api.php","trace":"#0 sys_layout_layout-api.edge.php(98): api\\data_table\\column_preferences\\remove_field_from_column()\n#1 edge.class.php(171): include()\n#2 edge.class.php(164): edge\\edge::phprender()\n#3 router.class.php(211): edge\\edge::render()\n#4 index.php(31): system\\router::route()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/data_table\/column_preferences\/remove_field_from_column","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-09-05 10:07:23] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(71) "/var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/test.cadservices.co.uk\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-05 10:07:23] [functions.php:194] \n<!--array(7) {\n  ["type"]: string(7) "Warning"\n  ["message"]: string(188) "Cannot modify header information - headers already sent by (output started at /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php:122)"\n  ["file"]: string(14) "adwsapi_v2.php"\n  ["line"]: int(102)\n  ["full_file"]: string(57) "/var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php"\n  ["request_uri"]: string(15) "/adwsapi_v2.php"\n  ["user_id"]: string(9) "Anonymous"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 194\n         <strong>Arguments:</strong> \n         0: {"type":"Warning","message":"Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)","file":"adwsapi_v2.php","line":102,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php","request_uri":"\/adwsapi_v2.php","user_id":"Anonymous"}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_error_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: 2\n         1: "Cannot modify header information - headers already sent by (output started at \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/functions\/functions.php:122)"\n         2: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/adwsapi_v2.php"\n         3: 102\n      <strong>Function:</strong> header, File: /var/www/vhosts/cadservices.co.uk/httpdocs/adwsapi_v2.php, Line: 102\n         <strong>Arguments:</strong> \n         0: "Content-Type: application\/json"\n-->\n
[php_errors] [2025-09-05 13:33:57] [functions.php:270] \n<!--array(7) {\n  ["type"]: string(22) "Fatal Error (Shutdown)"\n  ["message"]: string(93) "Cannot use system\data_table_storage as data_table_storage because the name is already in use"\n  ["file"]: string(50) "sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php"\n  ["line"]: int(1)\n  ["full_file"]: string(105) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 270\n         <strong>Arguments:</strong> \n         0: {"type":"Fatal Error (Shutdown)","message":"Cannot use system\\data_table_storage as data_table_storage because the name is already in use","file":"sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> {closure}, File: , Line: \n         <strong>Arguments:</strong> \n-->\n
[php_errors] [2025-09-05 13:33:59] [functions.php:270] \n<!--array(7) {\n  ["type"]: string(22) "Fatal Error (Shutdown)"\n  ["message"]: string(93) "Cannot use system\data_table_storage as data_table_storage because the name is already in use"\n  ["file"]: string(50) "sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php"\n  ["line"]: int(1)\n  ["full_file"]: string(105) "/var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 270\n         <strong>Arguments:</strong> \n         0: {"type":"Fatal Error (Shutdown)","message":"Cannot use system\\data_table_storage as data_table_storage because the name is already in use","file":"sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php","line":1,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> {closure}, File: , Line: \n         <strong>Arguments:</strong> \n-->\n
[php_errors] [2025-09-05 14:02:31] [functions.php:243] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(104) "system\data_table_storage::get_configuration(): Return value must be of type array|string, null returned"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(47)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(657) "#0 data_table_storage.class.php(671): system\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\edge::phprender()\n#10 router.class.php(202): edge\edge::renderView()\n#11 index.php(31): system\router::route()\n"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 243\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::get_configuration(): Return value must be of type array|string, null returned","file":"data_table_storage.class.php","line":47,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 data_table_storage.class.php(671): system\\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\\edge::phprender()\n#10 router.class.php(202): edge\\edge::renderView()\n#11 index.php(31): system\\router::route()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-09-05 14:02:34] [functions.php:243] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(104) "system\data_table_storage::get_configuration(): Return value must be of type array|string, null returned"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(47)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(657) "#0 data_table_storage.class.php(671): system\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\edge::phprender()\n#10 router.class.php(202): edge\edge::renderView()\n#11 index.php(31): system\router::route()\n"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 243\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::get_configuration(): Return value must be of type array|string, null returned","file":"data_table_storage.class.php","line":47,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 data_table_storage.class.php(671): system\\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\\edge::phprender()\n#10 router.class.php(202): edge\\edge::renderView()\n#11 index.php(31): system\\router::route()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-09-05 14:03:33] [functions.php:243] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(104) "system\data_table_storage::get_configuration(): Return value must be of type array|string, null returned"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(47)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(657) "#0 data_table_storage.class.php(671): system\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\edge::phprender()\n#10 router.class.php(202): edge\edge::renderView()\n#11 index.php(31): system\router::route()\n"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 243\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::get_configuration(): Return value must be of type array|string, null returned","file":"data_table_storage.class.php","line":47,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 data_table_storage.class.php(671): system\\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\\edge::phprender()\n#10 router.class.php(202): edge\\edge::renderView()\n#11 index.php(31): system\\router::route()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-09-05 14:04:02] [functions.php:243] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(104) "system\data_table_storage::get_configuration(): Return value must be of type array|string, null returned"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(47)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(657) "#0 data_table_storage.class.php(671): system\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\edge::phprender()\n#10 router.class.php(202): edge\edge::renderView()\n#11 index.php(31): system\router::route()\n"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 243\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::get_configuration(): Return value must be of type array|string, null returned","file":"data_table_storage.class.php","line":47,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 data_table_storage.class.php(671): system\\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\\edge::phprender()\n#10 router.class.php(202): edge\\edge::renderView()\n#11 index.php(31): system\\router::route()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-09-05 14:04:04] [functions.php:243] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(104) "system\data_table_storage::get_configuration(): Return value must be of type array|string, null returned"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(47)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(657) "#0 data_table_storage.class.php(671): system\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\edge::phprender()\n#10 router.class.php(202): edge\edge::renderView()\n#11 index.php(31): system\router::route()\n"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 243\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::get_configuration(): Return value must be of type array|string, null returned","file":"data_table_storage.class.php","line":47,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 data_table_storage.class.php(671): system\\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\\edge::phprender()\n#10 router.class.php(202): edge\\edge::renderView()\n#11 index.php(31): system\\router::route()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-09-05 14:04:13] [functions.php:243] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(104) "system\data_table_storage::get_configuration(): Return value must be of type array|string, null returned"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(47)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(657) "#0 data_table_storage.class.php(671): system\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\edge::phprender()\n#10 router.class.php(202): edge\edge::renderView()\n#11 index.php(31): system\router::route()\n"\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 243\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::get_configuration(): Return value must be of type array|string, null returned","file":"data_table_storage.class.php","line":47,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 data_table_storage.class.php(671): system\\data_table_storage::get_configuration()\n#1 sys_edge_data-table-structure.edge.php(32): system\\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\\edge::phprender()\n#4 sys_edge_data-table.edge.php(106): edge\\edge::render()\n#5 edge.class.php(171): include()\n#6 edge.class.php(164): edge\\edge::phprender()\n#7 sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(355): edge\\edge::render()\n#8 edge.class.php(171): include()\n#9 edge.class.php(205): edge\\edge::phprender()\n#10 router.class.php(202): edge\\edge::renderView()\n#11 index.php(31): system\\router::route()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
[php_errors] [2025-09-05 14:07:15] [functions.php:243] \n<!--array(9) {\n  ["type"]: string(18) "Uncaught Exception"\n  ["class"]: string(9) "TypeError"\n  ["message"]: string(104) "system\data_table_storage::get_configuration(): Return value must be of type array|string, null returned"\n  ["file"]: string(28) "data_table_storage.class.php"\n  ["line"]: int(47)\n  ["full_file"]: string(118) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php"\n  ["trace"]: string(566) "#0 data_table_storage.class.php(671): system\data_table_storage::get_configuration()\n#1 sys_edge_data-table.edge.php(39): system\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\edge::phprender()\n#4 logs.fn.php(52): edge\edge::render()\n#5 logs.view.php(39): system\logs\render_log_table()\n#6 sys_layout_layout-view.edge.php(22): include()\n#7 edge.class.php(171): include()\n#8 edge.class.php(164): edge\edge::phprender()\n#9 router.class.php(211): edge\edge::render()\n#10 index.php(31): system\router::route()\n"\n  ["request_uri"]: string(45) "/baffletrain/autocadlt/autobooks/system/logs/"\n  ["user_id"]: int(2)\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions/functions.php, Line: 243\n         <strong>Arguments:</strong> \n         0: {"type":"Uncaught Exception","class":"TypeError","message":"system\\data_table_storage::get_configuration(): Return value must be of type array|string, null returned","file":"data_table_storage.class.php","line":47,"full_file":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php","trace":"#0 data_table_storage.class.php(671): system\\data_table_storage::get_configuration()\n#1 sys_edge_data-table.edge.php(39): system\\data_table_storage::prepare_template_data()\n#2 edge.class.php(171): include()\n#3 edge.class.php(164): edge\\edge::phprender()\n#4 logs.fn.php(52): edge\\edge::render()\n#5 logs.view.php(39): system\\logs\\render_log_table()\n#6 sys_layout_layout-view.edge.php(22): include()\n#7 edge.class.php(171): include()\n#8 edge.class.php(164): edge\\edge::phprender()\n#9 router.class.php(211): edge\\edge::render()\n#10 index.php(31): system\\router::route()\n","request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/logs\/","user_id":2}\n         1: "php_errors"\n         2: true\n      <strong>Function:</strong> tcs_exception_handler, File: , Line: \n         <strong>Arguments:</strong> \n         0: {}\n-->\n
