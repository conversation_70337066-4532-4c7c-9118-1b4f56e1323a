[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 07:07:05
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => df08f4d9-7dbf-4301-bc8c-432e2c28e8b6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66187025913229\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T06:46:59.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T07:07:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 07:07:05
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => df08f4d9-7dbf-4301-bc8c-432e2c28e8b6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66187025913229\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T06:46:59.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T07:07:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66187025913229', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '66187025913229', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-04 07:07:05] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66187025913229', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '66187025913229', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 07:11:08
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 66d24bf1-bb3c-4a7b-96e7-4d198ee11b65\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66264904812166\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T06:43:38.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T07:11:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66264904812166', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '66264904812166', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 07:11:08
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 66d24bf1-bb3c-4a7b-96e7-4d198ee11b65\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66264904812166\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T06:43:38.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T07:11:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 07:11:08] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66264904812166', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '66264904812166', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-04 13:09:15] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 13:09:15
[subscription_update] [2025-09-04 13:09:15] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 13:09:15] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => da590026-9c7d-4db8-9abf-ef0b5306da84\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55388049199615\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-06\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-09-04T12:39:11.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T13:09:13.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 13:09:15] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-09-04 13:09:16] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 13:09:16
[subscription_update] [2025-09-04 13:09:16] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 13:09:16] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => da590026-9c7d-4db8-9abf-ef0b5306da84\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55388049199615\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-06\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-09-04T12:39:11.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T13:09:13.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 13:09:16] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-09-04 13:09:19] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55388049199615\n            [subscriptionReferenceNumber] => 561-82001311\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-03-31\n            [endDate] => 2026-09-06\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => USD\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => FENCO ALDRIDGE\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => FENCO ALDRIDGE\n                            [type] => End Customer\n                            [address1] => Willen Works Willen Road\n                            [address2] => \n                            [address3] => \n                            [city] => Newport Pagnell\n                            [stateProvince] => BUCKINGHAMSHIRE\n                            [postalCode] => MK16 0DG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Oil & Gas\n                            [primaryAdminFirstName] => Sharron\n                            [primaryAdminLastName] => Clegg\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 6917954\n                            [teamName] => Sharron Clegg - 7954\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Sharron\n                            [last] => Clegg\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-09-04 13:09:19] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-09-04 13:09:20] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55388049199615\n            [subscriptionReferenceNumber] => 561-82001311\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-03-31\n            [endDate] => 2026-09-06\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => USD\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => FENCO ALDRIDGE\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => FENCO ALDRIDGE\n                            [type] => End Customer\n                            [address1] => Willen Works Willen Road\n                            [address2] => \n                            [address3] => \n                            [city] => Newport Pagnell\n                            [stateProvince] => BUCKINGHAMSHIRE\n                            [postalCode] => MK16 0DG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Oil & Gas\n                            [primaryAdminFirstName] => Sharron\n                            [primaryAdminLastName] => Clegg\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 6917954\n                            [teamName] => Sharron Clegg - 7954\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Sharron\n                            [last] => Clegg\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-09-04 13:09:20] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 13:38:07
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2b1f022e-1915-4df5-8e39-9f7f48677624\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56891518358922\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-09-15\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-09-04T13:08:02.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T13:38:04.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56891518358922', status = 'Active', quantity = 1, endDate = '2028-09-15', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '56891518358922', status = 'Active', quantity = 1, endDate = '2028-09-15', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 13:38:07
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2b1f022e-1915-4df5-8e39-9f7f48677624\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56891518358922\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2028-09-15\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-09-04T13:08:02.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T13:38:04.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 13:38:07] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56891518358922', status = 'Active', quantity = 1, endDate = '2028-09-15', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '56891518358922', status = 'Active', quantity = 1, endDate = '2028-09-15', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 14:40:17
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a1e7a000-7124-48df-b0ad-cb8e387bbca2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73349919080022\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-09-04T14:20:12.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T14:40:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73349919080022', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73349919080022', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 14:40:17
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a1e7a000-7124-48df-b0ad-cb8e387bbca2\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73349919080022\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-09-04T14:20:12.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T14:40:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 14:40:17] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73349919080022', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73349919080022', autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-04 15:39:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 15:39:52
[subscription_update] [2025-09-04 15:39:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 15:39:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5c0e33ec-c310-415e-b2e6-8cea55b4ffdc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55388049199615\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T15:09:44.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T15:39:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 15:39:52] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-09-04 15:39:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 15:39:53
[subscription_update] [2025-09-04 15:39:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 15:39:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5c0e33ec-c310-415e-b2e6-8cea55b4ffdc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55388049199615\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T15:09:44.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T15:39:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 15:39:53] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-09-04 15:39:56] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55388049199615\n            [subscriptionReferenceNumber] => 561-82001311\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-03-31\n            [endDate] => 2026-09-06\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => FENCO ALDRIDGE\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => FENCO ALDRIDGE\n                            [type] => End Customer\n                            [address1] => Willen Works Willen Road\n                            [address2] => \n                            [address3] => \n                            [city] => Newport Pagnell\n                            [stateProvince] => BUCKINGHAMSHIRE\n                            [postalCode] => MK16 0DG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Oil & Gas\n                            [primaryAdminFirstName] => Sharron\n                            [primaryAdminLastName] => Clegg\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 6917954\n                            [teamName] => Sharron Clegg - 7954\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Sharron\n                            [last] => Clegg\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-09-04 15:39:56] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-09-04 15:40:00] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55388049199615\n            [subscriptionReferenceNumber] => 561-82001311\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-03-31\n            [endDate] => 2026-09-06\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => FENCO ALDRIDGE\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => FENCO ALDRIDGE\n                            [type] => End Customer\n                            [address1] => Willen Works Willen Road\n                            [address2] => \n                            [address3] => \n                            [city] => Newport Pagnell\n                            [stateProvince] => BUCKINGHAMSHIRE\n                            [postalCode] => MK16 0DG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Oil & Gas\n                            [primaryAdminFirstName] => Sharron\n                            [primaryAdminLastName] => Clegg\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 6917954\n                            [teamName] => Sharron Clegg - 7954\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Sharron\n                            [last] => Clegg\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-09-04 15:40:00] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 15:40:16
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2a6a1d88-a0a9-403a-ac31-1a9f69a356f3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56891518358922\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T15:10:07.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T15:40:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56891518358922', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56891518358922', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 15:40:16
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2a6a1d88-a0a9-403a-ac31-1a9f69a356f3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56891518358922\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T15:10:07.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T15:40:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-04 15:40:16] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56891518358922', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56891518358922', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-04 15:41:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 15:41:52
[subscription_update] [2025-09-04 15:41:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 15:41:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 34ac59f9-93fc-4a8e-b5a5-3931bdba580e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55536230799844\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-16\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-09-04T15:21:43.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T15:41:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 15:41:52] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-09-04 15:41:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 15:41:52
[subscription_update] [2025-09-04 15:41:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 15:41:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 34ac59f9-93fc-4a8e-b5a5-3931bdba580e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55536230799844\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-16\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-09-04T15:21:43.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T15:41:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 15:41:52] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-09-04 15:41:54] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55536230799844\n            [subscriptionReferenceNumber] => 564-79369855\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-04-17\n            [endDate] => 2026-09-16\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => USD\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surveying Solutions Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surveying Solutions Ltd\n                            [type] => End Customer\n                            [address1] => 34-36 Rose Street North Lane\n                            [address2] => \n                            [address3] => \n                            [city] => Edinburgh\n                            [stateProvince] => \n                            [postalCode] => EH2 2NP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => C\n                            [primaryAdminLastName] => Sutherland\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 7073614\n                            [teamName] => C Sutherland - 3614\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => C\n                            [last] => Sutherland\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-09-04 15:41:54] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-09-04 15:41:55] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55536230799844\n            [subscriptionReferenceNumber] => 564-79369855\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-04-17\n            [endDate] => 2026-09-16\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => USD\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surveying Solutions Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surveying Solutions Ltd\n                            [type] => End Customer\n                            [address1] => 34-36 Rose Street North Lane\n                            [address2] => \n                            [address3] => \n                            [city] => Edinburgh\n                            [stateProvince] => \n                            [postalCode] => EH2 2NP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => C\n                            [primaryAdminLastName] => Sutherland\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 7073614\n                            [teamName] => C Sutherland - 3614\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => C\n                            [last] => Sutherland\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-09-04 15:41:55] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-09-04 19:35:58] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 19:35:58
[subscription_update] [2025-09-04 19:35:58] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 19:35:58] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 572434d4-9525-4b89-af7e-a96c23d86e9f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55536230799844\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T19:10:46.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T19:35:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 19:35:58] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-09-04 19:35:58] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-04 19:35:58
[subscription_update] [2025-09-04 19:35:58] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-04 19:35:58] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 572434d4-9525-4b89-af7e-a96c23d86e9f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55536230799844\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-09-04T19:10:46.000+0000\n        )\n\n    [publishedAt] => 2025-09-04T19:35:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-04 19:35:58] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-09-04 19:36:02] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55536230799844\n            [subscriptionReferenceNumber] => 564-79369855\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-04-17\n            [endDate] => 2026-09-16\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surveying Solutions Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surveying Solutions Ltd\n                            [type] => End Customer\n                            [address1] => 34-36 Rose Street North Lane\n                            [address2] => \n                            [address3] => \n                            [city] => Edinburgh\n                            [stateProvince] => \n                            [postalCode] => EH2 2NP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => C\n                            [primaryAdminLastName] => Sutherland\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 7073614\n                            [teamName] => C Sutherland - 3614\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => C\n                            [last] => Sutherland\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-09-04 19:36:02] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-09-04 19:36:02] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55536230799844\n            [subscriptionReferenceNumber] => 564-79369855\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-04-17\n            [endDate] => 2026-09-16\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surveying Solutions Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surveying Solutions Ltd\n                            [type] => End Customer\n                            [address1] => 34-36 Rose Street North Lane\n                            [address2] => \n                            [address3] => \n                            [city] => Edinburgh\n                            [stateProvince] => \n                            [postalCode] => EH2 2NP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => C\n                            [primaryAdminLastName] => Sutherland\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 7073614\n                            [teamName] => C Sutherland - 3614\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => C\n                            [last] => Sutherland\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-09-04 19:36:02] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-05 06:34:26
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 7c80ac5c-466a-4f64-a4f8-3928272d82ba\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66065226907420\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-09-05T05:39:30.000+0000\n        )\n\n    [publishedAt] => 2025-09-05T06:34:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-05 06:34:26] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66065226907420', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '66065226907420', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-05 06:34:27
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 7c80ac5c-466a-4f64-a4f8-3928272d82ba\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66065226907420\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-09-05T05:39:30.000+0000\n        )\n\n    [publishedAt] => 2025-09-05T06:34:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-05 06:34:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66065226907420', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '66065226907420', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-05 09:05:34
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 78fb5207-ee2b-4f23-bd12-52d10a136ed7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66065226907420\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-09-05T06:41:52.000+0000\n        )\n\n    [publishedAt] => 2025-09-05T09:05:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66065226907420', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '66065226907420', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-05 09:05:34
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 78fb5207-ee2b-4f23-bd12-52d10a136ed7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66065226907420\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-09-05T06:41:52.000+0000\n        )\n\n    [publishedAt] => 2025-09-05T09:05:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-05 09:05:34] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66065226907420', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '66065226907420', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-05 12:16:33
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8d00e183-d661-4ffe-ba07-3247f3c16346\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55569793860285\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-09-05T09:08:51.000+0000\n        )\n\n    [publishedAt] => 2025-09-05T12:16:31.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55569793860285', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '55569793860285', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-05 12:16:33
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8d00e183-d661-4ffe-ba07-3247f3c16346\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55569793860285\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-09-05T09:08:51.000+0000\n        )\n\n    [publishedAt] => 2025-09-05T12:16:31.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-05 12:16:33] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55569793860285', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '55569793860285', autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-05 13:33:48
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4fcff3fb-d0a1-4ee7-9307-62f86009f3d0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73341386174790\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-09-05T10:45:04.000+0000\n        )\n\n    [publishedAt] => 2025-09-05T13:33:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73341386174790', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73341386174790', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-09-05 13:33:48
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4fcff3fb-d0a1-4ee7-9307-62f86009f3d0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 73341386174790\n            [quantity] => 1\n            [autoRenew] => OFF\n            [message] => subscription quantity,autoRenew changed.\n            [modifiedAt] => 2025-09-05T10:45:04.000+0000\n        )\n\n    [publishedAt] => 2025-09-05T13:33:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-09-05 13:33:48] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '73341386174790', quantity = 1, autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '73341386174790', quantity = 1, autoRenew = 'OFF';\n",\n        "affected_rows": 0\n    }\n]
